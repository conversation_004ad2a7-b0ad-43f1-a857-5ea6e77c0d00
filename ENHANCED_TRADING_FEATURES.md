# Enhanced Trading Bot with Support/Resistance and Scalping

## 🎯 New Features Implemented

### 1. **Technical Analysis Module** (`technical-analysis.js`)
- **Support/Resistance Detection**: Automatically identifies key price levels using pivot points
- **RSI Calculation**: Momentum indicator for overbought/oversold conditions
- **MACD Analysis**: Trend-following momentum indicator
- **Volume Analysis**: Detects volume spikes for confirmation
- **Signal Combination**: Weighted scoring system combining all indicators

### 2. **Scalping Manager** (`scalping-manager.js`)
- **Quick Trade Management**: Opens and closes trades within minutes
- **Risk Management**: Built-in stop-loss and profit targets
- **Trailing Stops**: Dynamic stop-loss adjustment for maximum profit
- **Position Sizing**: Intelligent trade amount calculation based on confidence
- **Performance Tracking**: Win rate, profit/loss statistics

### 3. **Enhanced Signal Generation**
- **Multi-Factor Analysis**: Combines LSTM predictions with technical indicators
- **Signal Types**: 
  - `normal`: Regular position trades (15% of balance)
  - `scalp`: Quick trades (5-15% of balance based on confidence)
- **Confidence Scoring**: 0-100% confidence based on signal alignment
- **Buy/Sell Scoring**: Weighted point system for decision making

## 📊 Signal Weighting System

| Indicator | Weight | Description |
|-----------|--------|-------------|
| LSTM Prediction | 40% | Neural network price prediction |
| Support/Resistance | 30% | Price level analysis |
| RSI | 15% | Momentum indicator |
| MACD | 15% | Trend indicator |
| Volume Spike | +20% | Confirmation multiplier |

## ⚡ Scalping Settings

```javascript
{
    maxConcurrentTrades: 3,        // Maximum simultaneous scalp trades
    quickProfitTarget: 0.008,      // 0.8% profit target
    stopLoss: 0.005,               // 0.5% stop loss
    maxHoldTime: 300000,           // 5 minutes maximum hold
    trailingStopEnabled: true,     // Dynamic stop loss
    trailingStopDistance: 0.003,   // 0.3% trailing distance
    minTradeAmount: 50000,         // Minimum 50k IDR per trade
    maxTradeAmount: 200000         // Maximum 200k IDR per trade
}
```

## 🎛️ Trading Strategy Logic

### Scalping Conditions (High Frequency)
- **Entry**: Enhanced signal = "scalp" + confidence > 60%
- **Frequency**: Every 3 seconds
- **Position Size**: 5-15% of balance
- **Hold Time**: Maximum 5 minutes
- **Profit Target**: 0.8%
- **Stop Loss**: 0.5%

### Regular Trading (Lower Frequency)
- **Entry**: Enhanced signal = "normal" + confidence > 50%
- **Position Size**: 15% for buy, 30% for sell
- **Strategy**: Longer-term position based on strong signals

## 📈 Technical Indicators

### RSI (Relative Strength Index)
- **Oversold**: < 30 (Buy signal)
- **Overbought**: > 70 (Sell signal)
- **Period**: 14 candles

### MACD (Moving Average Convergence Divergence)
- **Fast EMA**: 12 periods
- **Slow EMA**: 26 periods
- **Signal Line**: 9 periods
- **Buy**: MACD > Signal + Positive histogram
- **Sell**: MACD < Signal + Negative histogram

### Support/Resistance
- **Detection**: Pivot point analysis with 20-period lookback
- **Grouping**: Price levels within 0.2% tolerance
- **Strength**: Number of touches at each level
- **Usage**: Buy near support, sell near resistance

## 🌐 Web Interface Features

### New Panels Added:
1. **Scalping Panel**
   - Toggle scalping on/off
   - Active trades counter
   - Win rate percentage
   - Total profit/loss

2. **Technical Analysis Panel**
   - Current RSI value
   - MACD value
   - Support/resistance level counts
   - Enhanced signal display

### API Endpoints Added:
- `GET /api/scalping-stats` - Get scalping statistics
- `POST /api/scalping-settings` - Update scalping settings
- `GET /api/technical-analysis` - Get technical analysis data
- `POST /api/toggle-scalping` - Enable/disable scalping

## 🔧 Configuration

### Environment Variables
```bash
INDODAX_API_KEY=your_api_key
INDODAX_API_SECRET=your_api_secret
```

### Running the Bot
```bash
node index.js
```

### Web Interface
```
http://localhost:5000
```

## 📊 Performance Monitoring

The bot now tracks:
- **Individual Trade Performance**: Entry/exit prices, profit/loss, hold time
- **Overall Statistics**: Win rate, average profit, average loss
- **Signal Accuracy**: Confidence vs actual performance
- **Technical Indicator Effectiveness**: RSI, MACD, Support/Resistance hit rates

## 🚀 Key Improvements

1. **Reduced False Signals**: Multi-factor analysis reduces noise
2. **Better Risk Management**: Stop-loss and profit targets on all trades
3. **Scalping Capability**: Captures small price movements quickly
4. **Adaptive Learning**: Model continues to learn from prediction errors
5. **Real-time Monitoring**: Live updates of all indicators and trades

## ⚠️ Risk Management

- **Maximum Concurrent Trades**: Limited to 3 scalp trades
- **Position Sizing**: Controlled percentage of balance
- **Stop Losses**: Automatic exit on adverse moves
- **Time Limits**: Maximum hold time prevents stuck positions
- **Confidence Thresholds**: Only trade on high-confidence signals

The enhanced trading bot now combines the power of neural networks with traditional technical analysis and modern scalping techniques for a comprehensive trading solution!
