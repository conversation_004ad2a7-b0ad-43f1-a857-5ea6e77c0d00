// scalping-manager.js
// Manages scalping trades with quick entry/exit logic

class ScalpingManager {
    constructor() {
        this.activeTrades = new Map(); // Track active scalp trades
        this.tradeHistory = [];
        this.settings = {
            maxConcurrentTrades: 3,
            quickProfitTarget: 0.008,    // 0.8% profit target
            stopLoss: 0.005,             // 0.5% stop loss
            maxHoldTime: 60000,         // 1 minutes max
            trailingStopEnabled: true,
            trailingStopDistance: 0.003, // 0.3% trailing stop
            minTradeAmount: 50000,       // Minimum 50k IDR per trade
            maxTradeAmount: 200000       // Maximum 200k IDR per trade
        };
        this.profitStats = {
            totalTrades: 0,
            profitableTrades: 0,
            totalProfit: 0,
            totalLoss: 0,
            winRate: 0,
            avgProfit: 0,
            avgLoss: 0
        };
    }

    // Check if we can open a new scalp trade
    canOpenTrade() {
        return this.activeTrades.size < this.settings.maxConcurrentTrades;
    }

    // Open a new scalp trade
    openScalpTrade(signal, currentPrice, balance, api, isTestMode = true) {
        if (!this.canOpenTrade()) {
            console.log('🚫 Cannot open scalp trade: Max concurrent trades reached');
            return null;
        }

        const tradeId = `scalp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const tradeAmount = this.calculateTradeAmount(balance, signal.confidence);
        
        if (tradeAmount < this.settings.minTradeAmount) {
            console.log('🚫 Trade amount too small for scalping');
            return null;
        }

        const trade = {
            id: tradeId,
            type: signal.signal, // 'buy' or 'sell'
            entryPrice: currentPrice,
            entryTime: Date.now(),
            amount: tradeAmount,
            confidence: signal.confidence,
            signalType: signal.type,
            stopLoss: this.calculateStopLoss(currentPrice, signal.signal),
            profitTarget: this.calculateProfitTarget(currentPrice, signal.signal),
            trailingStop: null,
            status: 'open',
            indicators: signal.indicators
        };

        // Execute the trade
        this.executeTrade(trade, api, isTestMode);
        
        // Store the trade
        this.activeTrades.set(tradeId, trade);
        
        console.log(`🎯 Opened scalp ${trade.type.toUpperCase()} trade:`);
        console.log(`   ID: ${tradeId}`);
        console.log(`   Entry: ${currentPrice.toLocaleString('id-ID')} IDR`);
        console.log(`   Amount: ${tradeAmount.toLocaleString('id-ID')} IDR`);
        console.log(`   Target: ${trade.profitTarget.toLocaleString('id-ID')} IDR`);
        console.log(`   Stop Loss: ${trade.stopLoss.toLocaleString('id-ID')} IDR`);
        console.log(`   Confidence: ${(signal.confidence * 100).toFixed(1)}%`);

        return trade;
    }

    // Calculate trade amount based on balance and confidence
    calculateTradeAmount(balance, confidence) {
        // Use 5-15% of balance based on confidence
        const basePercentage = 0.05; // 5%
        const confidenceBonus = confidence * 0.10; // Up to 10% more
        const percentage = Math.min(basePercentage + confidenceBonus, 0.15);
        
        const amount = balance * percentage;
        return Math.min(Math.max(amount, this.settings.minTradeAmount), this.settings.maxTradeAmount);
    }

    // Calculate stop loss price
    calculateStopLoss(entryPrice, tradeType) {
        if (tradeType === 'buy') {
            return entryPrice * (1 - this.settings.stopLoss);
        } else {
            return entryPrice * (1 + this.settings.stopLoss);
        }
    }

    // Calculate profit target price
    calculateProfitTarget(entryPrice, tradeType) {
        if (tradeType === 'buy') {
            return entryPrice * (1 + this.settings.quickProfitTarget);
        } else {
            return entryPrice * (1 - this.settings.quickProfitTarget);
        }
    }

    // Execute the actual trade
    async executeTrade(trade, api, isTestMode) {
        try {
            if (trade.type === 'buy') {
                await api.trade('btc_idr', 'buy', trade.entryPrice, {
                    amountIdr: trade.amount,
                    isTestMode
                });
            } else {
                // For sell trades, calculate BTC amount
                const btcAmount = trade.amount / trade.entryPrice;
                await api.trade('btc_idr', 'sell', trade.entryPrice, {
                    amountBtc: btcAmount,
                    isTestMode
                });
            }
        } catch (error) {
            console.error('Error executing scalp trade:', error);
            trade.status = 'error';
        }
    }

    // Monitor all active trades
    monitorTrades(currentPrice, api, isTestMode = true) {
        const currentTime = Date.now();
        const tradesToClose = [];

        for (const [tradeId, trade] of this.activeTrades) {
            const shouldClose = this.shouldCloseTrade(trade, currentPrice, currentTime);
            
            if (shouldClose.close) {
                tradesToClose.push({
                    trade,
                    reason: shouldClose.reason,
                    exitPrice: currentPrice
                });
            } else if (this.settings.trailingStopEnabled) {
                this.updateTrailingStop(trade, currentPrice);
            }
        }

        // Close trades that need to be closed
        tradesToClose.forEach(({ trade, reason, exitPrice }) => {
            this.closeTrade(trade, exitPrice, reason, api, isTestMode);
        });

        return {
            activeTradesCount: this.activeTrades.size,
            closedTradesCount: tradesToClose.length,
            closedTrades: tradesToClose
        };
    }

    // Check if a trade should be closed
    shouldCloseTrade(trade, currentPrice, currentTime) {
        // Check time limit
        if (currentTime - trade.entryTime > this.settings.maxHoldTime) {
            return { close: true, reason: 'time_limit' };
        }

        // Check profit target
        if (trade.type === 'buy' && currentPrice >= trade.profitTarget) {
            return { close: true, reason: 'profit_target' };
        }
        if (trade.type === 'sell' && currentPrice <= trade.profitTarget) {
            return { close: true, reason: 'profit_target' };
        }

        // Check stop loss
        if (trade.type === 'buy' && currentPrice <= trade.stopLoss) {
            return { close: true, reason: 'stop_loss' };
        }
        if (trade.type === 'sell' && currentPrice >= trade.stopLoss) {
            return { close: true, reason: 'stop_loss' };
        }

        // Check trailing stop
        if (trade.trailingStop) {
            if (trade.type === 'buy' && currentPrice <= trade.trailingStop) {
                return { close: true, reason: 'trailing_stop' };
            }
            if (trade.type === 'sell' && currentPrice >= trade.trailingStop) {
                return { close: true, reason: 'trailing_stop' };
            }
        }

        return { close: false };
    }

    // Update trailing stop
    updateTrailingStop(trade, currentPrice) {
        if (trade.type === 'buy') {
            const newTrailingStop = currentPrice * (1 - this.settings.trailingStopDistance);
            if (!trade.trailingStop || newTrailingStop > trade.trailingStop) {
                trade.trailingStop = newTrailingStop;
            }
        } else {
            const newTrailingStop = currentPrice * (1 + this.settings.trailingStopDistance);
            if (!trade.trailingStop || newTrailingStop < trade.trailingStop) {
                trade.trailingStop = newTrailingStop;
            }
        }
    }

    // Close a trade
    async closeTrade(trade, exitPrice, reason, api, isTestMode) {
        try {
            // Execute closing trade
            if (trade.type === 'buy') {
                // Sell the BTC we bought
                const btcAmount = trade.amount / trade.entryPrice;
                await api.trade('btc_idr', 'sell', exitPrice, {
                    amountBtc: btcAmount,
                    isTestMode
                });
            } else {
                // Buy back the BTC we sold
                const idrAmount = (trade.amount / trade.entryPrice) * exitPrice;
                await api.trade('btc_idr', 'buy', exitPrice, {
                    amountIdr: idrAmount,
                    isTestMode
                });
            }

            // Calculate profit/loss
            const profit = this.calculateProfit(trade, exitPrice);
            
            // Update trade record
            trade.exitPrice = exitPrice;
            trade.exitTime = Date.now();
            trade.profit = profit;
            trade.closeReason = reason;
            trade.status = 'closed';
            trade.holdTime = trade.exitTime - trade.entryTime;

            // Update statistics
            this.updateProfitStats(trade);

            // Move to history and remove from active
            this.tradeHistory.unshift(trade);
            this.activeTrades.delete(trade.id);

            // Keep only last 100 trades in history
            if (this.tradeHistory.length > 100) {
                this.tradeHistory = this.tradeHistory.slice(0, 100);
            }

            const profitPercent = (profit / trade.amount) * 100;
            const holdTimeMinutes = trade.holdTime / 60000;

            console.log(`🏁 Closed scalp ${trade.type.toUpperCase()} trade:`);
            console.log(`   ID: ${trade.id}`);
            console.log(`   Entry: ${trade.entryPrice.toLocaleString('id-ID')} IDR`);
            console.log(`   Exit: ${exitPrice.toLocaleString('id-ID')} IDR`);
            console.log(`   Profit: ${profit.toLocaleString('id-ID')} IDR (${profitPercent.toFixed(2)}%)`);
            console.log(`   Reason: ${reason}`);
            console.log(`   Hold Time: ${holdTimeMinutes.toFixed(1)} minutes`);

        } catch (error) {
            console.error('Error closing scalp trade:', error);
        }
    }

    // Calculate profit/loss for a trade
    calculateProfit(trade, exitPrice) {
        if (trade.type === 'buy') {
            const btcAmount = trade.amount / trade.entryPrice;
            return (btcAmount * exitPrice) - trade.amount;
        } else {
            const btcAmount = trade.amount / trade.entryPrice;
            return trade.amount - (btcAmount * exitPrice);
        }
    }

    // Update profit statistics
    updateProfitStats(trade) {
        this.profitStats.totalTrades++;
        
        if (trade.profit > 0) {
            this.profitStats.profitableTrades++;
            this.profitStats.totalProfit += trade.profit;
        } else {
            this.profitStats.totalLoss += Math.abs(trade.profit);
        }

        this.profitStats.winRate = (this.profitStats.profitableTrades / this.profitStats.totalTrades) * 100;
        this.profitStats.avgProfit = this.profitStats.totalProfit / Math.max(this.profitStats.profitableTrades, 1);
        this.profitStats.avgLoss = this.profitStats.totalLoss / Math.max(this.profitStats.totalTrades - this.profitStats.profitableTrades, 1);
    }

    // Get scalping statistics
    getScalpingStats() {
        return {
            activeTrades: Array.from(this.activeTrades.values()),
            recentTrades: this.tradeHistory.slice(0, 10),
            profitStats: this.profitStats,
            settings: this.settings
        };
    }

    // Update scalping settings
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        console.log('📊 Scalping settings updated:', newSettings);
    }
}

module.exports = ScalpingManager;
