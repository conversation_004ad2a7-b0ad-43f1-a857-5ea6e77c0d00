// index.js

// Main Node.js application file for the Indodax Trading Bot using Express



const path = require('path');

const crypto = require('crypto');

const express = require('express');

const axios = require('axios');

const LSTMPredictor = require('./lstm-model');

require('dotenv').config();



// --- Configuration ---

const app = express();

const PORT = process.env.PORT || 5000;



// Middleware to parse JSON bodies

app.use(express.json());

// Middleware to serve the static HTML file

app.use(express.static(path.join(__dirname, 'public')));





// --- Global State ---

let botState = {

    isRunning: false,

    status: "Stopped",

    lastPrice: 1727000000,

    balanceIdr: 500000,

    balanceBtc: 0.00014429,

    predictedPrice: 0,

    predictionConfidence: 0,

    lastSignal: 'hold',

    lastPredictionData: null, // Store last prediction for online learning

    tradeLog: [],

    historicalData: [], // This will be an array of {timestamp, open, high, low, close, volume}

    isTestMode: true,

    testBalanceIdr: 500000,

    testBalanceBtc: 0.00014429,

    modelInfo: {
        isTraining: false,
        hasModel: false,
        lastPrediction: null
    }

};

let tradeInterval = null; // To hold the setInterval handle

let lstmPredictor = new LSTMPredictor(); // Initialize LSTM predictor



// --- Indodax API Client ---

class IndodaxAPI {

    constructor(apiKey, apiSecret) {

        this.apiKey = apiKey;

        this.apiSecret = apiSecret;

        this.baseUrl = "https://indodax.com";

    }



    _signRequest(params) {

        if (!this.apiKey || !this.apiSecret) {

            throw new Error("API Key and Secret must be provided for private calls.");

        }

        params.nonce = Date.now();

        const body = new URLSearchParams(params).toString();

        const signature = crypto.createHmac('sha512', this.apiSecret).update(body).digest('hex');



        return {

            body,

            headers: {

                'Key': this.apiKey,

                'Sign': signature,

                'Content-Type': 'application/x-www-form-urlencoded'

            }

        };

    }



    async getRecentTrades(pair = 'btc_idr') {

        try {

            const response = await axios.get(`${this.baseUrl}/api/${pair}/trades`);

            return response.data;

        } catch (error) {

            console.error(`Error fetching recent trades: ${error.message}`);

            return null;

        }

    }



    async getInfo() {

        try {

            const { body, headers } = this._signRequest({ method: 'getInfo' });

            const response = await axios.post(`${this.baseUrl}/tapi`, body, { headers });

            if (response.data && response.data.success === 1) {

                return response.data.return;

            } else {

                console.error(`Error in getInfo API call: ${response.data.error}`);

                return null;

            }

        } catch (error) {

            console.error(`Error in getInfo: ${error.message}`);

            return null;

        }

    }



    async getHistoricalData(pair = 'btc_idr', timeframeMinutes = 60, candleCount = 200) {

        try {

            const toTimestamp = Math.floor(Date.now() / 1000);

            const fromTimestamp = toTimestamp - (candleCount * timeframeMinutes * 60);







            const url = `${this.baseUrl}/tradingview/history_v2`;

            // symbol=btcidr&tf=15&from=1749449031&to=1749537231

            const now = Math.floor(Date.now() / 1000)

            const response = await axios.get(url, { params: { symbol: 'btcidr', tf: 1, from: now - (60 * 60 * 1000), to: now } });

            const data = response.data;



            if (data.length > 0) {

                console.log(data[0])

                // Convert TradingView format to an array of objects

                return data.map((dt) => ({

                    timestamp: dt.Time,

                    open: dt.Open,

                    high: dt.High,

                    low: dt.Low,

                    close: dt.Close,

                    volume: dt.Volume,

                }));

            } else {

                console.warn(`Historical data received in an unexpected format: ${data.s || 'No status'}`);

                return [];

            }

        } catch (error) {

            console.error(`Error fetching or parsing historical data: ${error.message}`);

            return [];

        }

    }



    async trade(pair, type, price, { amountIdr = null, amountBtc = null, isTestMode = true }) {

        try {

            if (isTestMode) {

                const logPrefix = "[PAPER TRADE]";

                if (type === 'buy' && amountIdr > 0) {

                    const btcToBuy = amountIdr / price;

                    if (botState.testBalanceIdr >= amountIdr) {

                        botState.testBalanceIdr -= amountIdr;

                        botState.testBalanceBtc += btcToBuy;

                        const logMsg = `${logPrefix} BUY ${btcToBuy.toFixed(8)} BTC`;

                        console.info(logMsg);

                        botState.tradeLog.unshift(`[${new Date().toISOString()}] ${logMsg} executed.`);

                    }

                } else if (type === 'sell' && amountBtc > 0) {

                    if (botState.testBalanceBtc >= amountBtc) {

                        const idrToGet = amountBtc * price;

                        botState.testBalanceBtc -= amountBtc;

                        botState.testBalanceIdr += idrToGet;

                        const logMsg = `${logPrefix} SELL ${amountBtc.toFixed(8)} BTC`;

                        console.info(logMsg);

                        botState.tradeLog.unshift(`[${new Date().toISOString()}] ${logMsg} executed.`);

                    }

                }

            } else {

                console.info(`REAL TRADE: ${type} ${pair}...`);

                // Real trading logic would be implemented here. It is disabled for safety.

                // const params = { method: 'trade', pair, type, price };

                // if (type === 'buy') params.idr = amountIdr;

                // else params[pair.split('_')[0]] = amountBtc;

                // const { body, headers } = this._signRequest(params);

                // await axios.post(`${this.baseUrl}/tapi`, body, { headers });

            }



        } catch (error) {

            console.error('trade', error)

        }

    }

}



// --- Trading Algorithm ---

const lstmTradingStrategy = async (isTestMode = true) => {

    const pair = 'btc_idr';

    const apiKey = process.env.INDODAX_API_KEY;

    const apiSecret = process.env.INDODAX_API_SECRET;



    if (!isTestMode && (!apiKey || !apiSecret)) {

        botState.status = "Error: API credentials not set for Live Mode.";

        console.error(botState.status);

        botState.isRunning = false;

        if (tradeInterval) clearInterval(tradeInterval);

        return;

    }



    const api = new IndodaxAPI(apiKey, apiSecret);



    try {

        const trades = await api.getRecentTrades(pair);

        if (!trades || trades.length === 0) return;



        const currentPrice = parseFloat(trades[0].price);

        botState.lastPrice = currentPrice;



        // Online learning: Update model with previous prediction if available

        if (botState.lastPredictionData && botState.lastPredictionData.predictedPrice) {

            const actualPrice = currentPrice;

            const predictedPrice = botState.lastPredictionData.predictedPrice;

            const inputSequence = botState.lastPredictionData.inputSequence;



            // Normalize actual price for comparison

            const normalizedActual = (actualPrice - lstmPredictor.scaler.min[3]) /

                                   (lstmPredictor.scaler.max[3] - lstmPredictor.scaler.min[3]);

            const normalizedPredicted = (predictedPrice - lstmPredictor.scaler.min[3]) /

                                      (lstmPredictor.scaler.max[3] - lstmPredictor.scaler.min[3]);



            // Update model with actual result

            lstmPredictor.updateWithNewData(normalizedActual, normalizedPredicted, inputSequence);

        }



        // Ensure we have enough historical data

        if (botState.historicalData.length < lstmPredictor.sequenceLength + 10) {

            console.log("Collecting more historical data for LSTM...");

            return;

        }



        // Make prediction using LSTM

        const prediction = await lstmPredictor.predict(botState.historicalData);



        if (prediction) {

            botState.predictedPrice = prediction.predictedPrice;

            botState.predictionConfidence = prediction.confidence;



            // Store prediction data for next online learning update

            botState.lastPredictionData = {

                predictedPrice: prediction.predictedPrice,

                inputSequence: prediction.inputSequence,

                timestamp: Date.now()

            };



            // Generate trading signal

            const signal = lstmPredictor.generateSignal(prediction, currentPrice);

            botState.lastSignal = signal;



            console.log(`Current: ${currentPrice}, Predicted: ${prediction.predictedPrice.toFixed(2)}, Confidence: ${(prediction.confidence * 100).toFixed(1)}%, Signal: ${signal}`);

        }



        // Update model info

        botState.modelInfo = lstmPredictor.getModelInfo();



        // Update balances

        if (!isTestMode) {

            const info = await api.getInfo();

            if (info && info.balance) {

                botState.balanceIdr = parseFloat(info.balance.idr) || 0;

                botState.balanceBtc = parseFloat(info.balance.btc) || 0;

            }

        } else {

            botState.balanceIdr = botState.testBalanceIdr;

            botState.balanceBtc = botState.testBalanceBtc;

        }



        // Trading Logic based on LSTM predictions

        const currentIdrBalance = isTestMode ? botState.testBalanceIdr : botState.balanceIdr;

        const currentBtcBalance = isTestMode ? botState.testBalanceBtc : botState.balanceBtc;



        if (prediction && prediction.confidence > 0.3) {

            const signal = lstmPredictor.generateSignal(prediction, currentPrice);



            if (signal === 'buy' && currentIdrBalance > 0) {

                console.log("LSTM BUY SIGNAL DETECTED");

                const buyAmount = currentIdrBalance * 0.1; // Use 10% of balance

                await api.trade(pair, 'buy', currentPrice, { amountIdr: buyAmount, isTestMode });

            } else if (signal === 'sell' && currentBtcBalance > 0) {

                console.log("LSTM SELL SIGNAL DETECTED");

                const sellAmount = currentBtcBalance * 0.5; // Sell 50% of BTC

                await api.trade(pair, 'sell', currentPrice, { amountBtc: sellAmount, isTestMode });

            }

        }



        // Add current data to historical data

        botState.historicalData.push({

            timestamp: Date.now(),

            open: currentPrice, // Using current price as approximation

            high: currentPrice,

            low: currentPrice,

            close: currentPrice,

            volume: trades[0].amount || 0

        });



        // Maintain historical data size

        if (botState.historicalData.length > 500) {

            botState.historicalData.shift();

        }



    } catch (error) {

        console.error(`An error occurred in the LSTM trading loop: ${error.message}`);

    }

};









// --- API Routes ---

app.get('/', (req, res) => {

    res.sendFile(path.join(__dirname, 'public', 'index.html'));

});



app.get('/api/data', (req, res) => {

    // Return a copy of the state for thread safety (good practice)

    res.json({ ...botState });

});



app.post('/api/start', async (req, res) => {

    if (botState.isRunning) {

        return res.status(400).json({ status: "Bot is already running." });

    }



    const { isTestMode } = req.body;



    botState.isRunning = true;

    botState.status = "Initializing...";

    botState.isTestMode = isTestMode;



    console.log(`Starting bot in ${isTestMode ? 'Test' : 'Live'} Mode.`);



    if (isTestMode) {

        botState.tradeLog = [];

    }



    const api = new IndodaxAPI();

    botState.historicalData = await api.getHistoricalData('btc_idr', 60, 250);



    if (botState.historicalData.length < lstmPredictor.sequenceLength + 20) {

        botState.isRunning = false;

        botState.status = "Error: Could not fetch enough initial history for LSTM.";

        return res.status(500).json({ status: botState.status });

    }



    // Quick initial training with minimal epochs (rely on online learning)

    botState.status = "Quick initial training...";

    console.log("Performing quick initial training, will learn continuously...");



    const trainingSuccess = await lstmPredictor.trainModel(botState.historicalData, 20);



    if (!trainingSuccess) {

        console.log("Initial training failed, but continuing with online learning only...");

        // Don't fail - just rely on online learning

    }



    // Reset prediction data for fresh start

    botState.lastPredictionData = null;



    // Start the trading loop

    botState.status = "Running...";

    lstmTradingStrategy(isTestMode); // Run once immediately

    tradeInterval = setInterval(() => lstmTradingStrategy(isTestMode), 5 * 1000); // Run every 5 seconds



    res.json({ status: "Bot starting..." });

});



app.post('/api/stop', (req, res) => {

    if (!botState.isRunning) {

        return res.status(400).json({ status: "Bot is not running." });

    }



    if (tradeInterval) {

        clearInterval(tradeInterval);

        tradeInterval = null;

    }



    botState.isRunning = false;

    botState.status = "Stopped";

    console.log("Bot has been stopped by user.");



    res.json({ status: "Bot stopping..." });

});



app.post('/api/retrain', async (req, res) => {

    if (botState.isRunning) {

        return res.status(400).json({ status: "Cannot retrain while bot is running. Stop the bot first." });

    }



    if (botState.historicalData.length < lstmPredictor.sequenceLength + 50) {

        return res.status(400).json({ status: "Not enough historical data for retraining." });

    }



    try {

        console.log("Starting LSTM model retraining...");

        const success = await lstmPredictor.trainModel(botState.historicalData, 50);



        if (success) {

            res.json({ status: "Model retrained successfully." });

        } else {

            res.status(500).json({ status: "Failed to retrain model." });

        }

    } catch (error) {

        console.error("Error during retraining:", error);

        res.status(500).json({ status: "Error during retraining." });

    }

});



app.get('/api/model-info', (req, res) => {

    const modelInfo = lstmPredictor.getModelInfo();

    res.json({

        ...modelInfo,

        historicalDataLength: botState.historicalData.length,

        requiredDataLength: lstmPredictor.sequenceLength + 20,

        lastPredictionData: botState.lastPredictionData

    });

});



app.post('/api/toggle-online-learning', (req, res) => {

    lstmPredictor.onlineLearning = !lstmPredictor.onlineLearning;



    res.json({

        status: `Online learning ${lstmPredictor.onlineLearning ? 'enabled' : 'disabled'}`,

        onlineLearning: lstmPredictor.onlineLearning

    });

});



app.get('/api/learning-stats', (req, res) => {

    const stats = lstmPredictor.getLearningStats();

    res.json(stats);

});



// Reset model endpoint
app.post('/api/reset-model', (req, res) => {
    try {
        const success = lstmPredictor.resetModel();
        if (success) {
            res.json({
                success: true,
                message: 'Model has been reset successfully. The bot will retrain on next prediction.'
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Failed to reset model'
            });
        }
    } catch (error) {
        console.error('Error resetting model:', error);
        res.status(500).json({
            success: false,
            message: 'Error resetting model: ' + error.message
        });
    }
});



app.get('/api/debug-prediction', async (req, res) => {

    try {

        if (botState.historicalData.length < lstmPredictor.sequenceLength) {

            return res.json({ error: "Not enough data for prediction" });

        }



        const prediction = await lstmPredictor.predict(botState.historicalData);

        const currentPrice = botState.lastPrice;



        if (prediction) {

            const priceChange = (prediction.predictedPrice - currentPrice) / currentPrice;

            const signal = lstmPredictor.generateSignal(prediction, currentPrice);



            res.json({

                currentPrice: currentPrice,

                predictedPrice: prediction.predictedPrice,

                priceChange: priceChange,

                priceChangePercent: (priceChange * 100) + '%',

                confidence: prediction.confidence,

                signal: signal,

                threshold: 0.03,

                minConfidence: 0.4

            });

        } else {

            res.json({ error: "Failed to make prediction" });

        }



    } catch (error) {

        res.status(500).json({ error: error.message });

    }

});



app.post('/api/save-model', (req, res) => {

    try {

        const success = lstmPredictor.saveModel();



        if (success) {

            res.json({ status: "Model saved successfully" });

        } else {

            res.status(500).json({ status: "Failed to save model" });

        }



    } catch (error) {

        res.status(500).json({ error: error.message });

    }

});



app.post('/api/load-model', (req, res) => {

    try {

        const success = lstmPredictor.loadModel();



        if (success) {

            res.json({ status: "Model loaded successfully" });

        } else {

            res.json({ status: "No saved model found, using new model" });

        }



    } catch (error) {

        res.status(500).json({ error: error.message });

    }

});



// --- Server Start ---

app.listen(PORT, () => {

    console.log(`Server is running on http://localhost:${PORT}`);

});

