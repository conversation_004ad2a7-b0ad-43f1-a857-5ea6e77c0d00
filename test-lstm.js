// test-lstm.js
// Simple test for the LSTM predictor

const LSTMPredictor = require('./lstm-model');

// Generate some sample data
function generateSampleData(length = 100) {
    const data = [];
    let price = 50000; // Starting price
    
    for (let i = 0; i < length; i++) {
        // Simple random walk with trend
        const change = (Math.random() - 0.5) * 1000 + (Math.sin(i / 10) * 200);
        price += change;
        
        data.push({
            timestamp: Date.now() + i * 60000,
            open: price + (Math.random() - 0.5) * 100,
            high: price + Math.random() * 200,
            low: price - Math.random() * 200,
            close: price,
            volume: Math.random() * 1000000
        });
    }
    
    return data;
}

async function testOnlineLearning() {
    console.log("Testing Online Learning LSTM Predictor...");

    // Create predictor
    const predictor = new LSTMPredictor(10, 5); // Smaller sequence for testing

    // Generate initial training data
    const initialData = generateSampleData(50);
    console.log(`Generated ${initialData.length} initial data points`);

    // Quick initial training
    console.log("Quick initial training...");
    await predictor.trainModel(initialData, 20);

    // Generate new data points to simulate real-time trading
    console.log("\nSimulating online learning with new data...");

    for (let i = 0; i < 20; i++) {
        // Add new data point
        const newPrice = initialData[initialData.length - 1].close + (Math.random() - 0.5) * 1000;
        const newDataPoint = {
            timestamp: Date.now() + i * 60000,
            open: newPrice + (Math.random() - 0.5) * 100,
            high: newPrice + Math.random() * 200,
            low: newPrice - Math.random() * 200,
            close: newPrice,
            volume: Math.random() * 1000000
        };

        initialData.push(newDataPoint);

        // Make prediction
        const prediction = await predictor.predict(initialData);

        if (prediction) {
            console.log(`\nStep ${i + 1}:`);
            console.log(`Current: ${prediction.currentPrice.toFixed(2)}, Predicted: ${prediction.predictedPrice.toFixed(2)}`);

            // Simulate getting actual next price (for online learning)
            const actualNextPrice = newPrice + (Math.random() - 0.5) * 500;

            // Update model with actual result
            const normalizedActual = (actualNextPrice - predictor.scaler.min[3]) /
                                   (predictor.scaler.max[3] - predictor.scaler.min[3]);
            const normalizedPredicted = (prediction.predictedPrice - predictor.scaler.min[3]) /
                                      (predictor.scaler.max[3] - predictor.scaler.min[3]);

            predictor.updateWithNewData(normalizedActual, normalizedPredicted, prediction.inputSequence);

            // Show learning stats every 5 steps
            if ((i + 1) % 5 === 0) {
                const stats = predictor.getLearningStats();
                console.log(`Learning Stats: Predictions=${stats.totalPredictions}, Accuracy=${(stats.accuracy * 100).toFixed(1)}%`);
            }
        }
    }

    // Final model info
    console.log("\nFinal Model Info:");
    const modelInfo = predictor.getModelInfo();
    console.log(JSON.stringify(modelInfo, null, 2));
}

// Run the test
testOnlineLearning().catch(console.error);
