// technical-analysis.js
// Advanced Technical Analysis for Support/Resistance and Scalping

class TechnicalAnalysis {
    constructor() {
        this.supportLevels = [];
        this.resistanceLevels = [];
        this.pivotPoints = [];
        this.scalingSettings = {
            quickProfitThreshold: 0.008, // 0.8% for quick scalp
            stopLossThreshold: 0.005,    // 0.5% stop loss
            maxHoldTime: 300000,         // 5 minutes max hold for scalp
            volumeThreshold: 1.5         // Volume spike threshold
        };
    }

    // Calculate Support and Resistance levels using pivot points
    calculateSupportResistance(historicalData, lookback = 20) {
        if (historicalData.length < lookback * 2) return { supports: [], resistances: [] };

        const pivots = this.findPivotPoints(historicalData, lookback);
        const supports = [];
        const resistances = [];

        // Group similar price levels
        const tolerance = this.calculatePriceTolerance(historicalData);

        pivots.forEach(pivot => {
            if (pivot.type === 'low') {
                this.addToLevel(supports, pivot.price, tolerance, pivot.strength);
            } else if (pivot.type === 'high') {
                this.addToLevel(resistances, pivot.price, tolerance, pivot.strength);
            }
        });

        // Sort by strength (most touches first)
        supports.sort((a, b) => b.strength - a.strength);
        resistances.sort((a, b) => b.strength - a.strength);

        this.supportLevels = supports.slice(0, 5); // Keep top 5
        this.resistanceLevels = resistances.slice(0, 5);

        return {
            supports: this.supportLevels,
            resistances: this.resistanceLevels
        };
    }

    // Find pivot points (local highs and lows)
    findPivotPoints(data, lookback = 10) {
        const pivots = [];
        
        for (let i = lookback; i < data.length - lookback; i++) {
            const current = data[i];
            const leftSide = data.slice(i - lookback, i);
            const rightSide = data.slice(i + 1, i + lookback + 1);

            // Check for pivot high
            const isHigh = leftSide.every(candle => candle.high <= current.high) &&
                          rightSide.every(candle => candle.high <= current.high);

            // Check for pivot low
            const isLow = leftSide.every(candle => candle.low >= current.low) &&
                         rightSide.every(candle => candle.low >= current.low);

            if (isHigh) {
                pivots.push({
                    type: 'high',
                    price: current.high,
                    timestamp: current.timestamp,
                    index: i,
                    strength: 1,
                    volume: current.volume || 0
                });
            }

            if (isLow) {
                pivots.push({
                    type: 'low',
                    price: current.low,
                    timestamp: current.timestamp,
                    index: i,
                    strength: 1,
                    volume: current.volume || 0
                });
            }
        }

        return pivots;
    }

    // Add price to support/resistance level or create new level
    addToLevel(levels, price, tolerance, strength = 1) {
        const existingLevel = levels.find(level => 
            Math.abs(level.price - price) <= tolerance
        );

        if (existingLevel) {
            existingLevel.strength += strength;
            existingLevel.price = (existingLevel.price + price) / 2; // Average the prices
        } else {
            levels.push({
                price: price,
                strength: strength,
                lastTouch: Date.now()
            });
        }
    }

    // Calculate price tolerance for grouping levels
    calculatePriceTolerance(data) {
        const recentData = data.slice(-50);
        const prices = recentData.map(d => d.close);
        const avgPrice = prices.reduce((sum, p) => sum + p, 0) / prices.length;
        return avgPrice * 0.002; // 0.2% tolerance
    }

    // Calculate RSI for momentum
    calculateRSI(data, period = 14) {
        if (data.length < period + 1) return 50;

        const prices = data.slice(-period - 1).map(d => d.close);
        let gains = 0;
        let losses = 0;

        for (let i = 1; i < prices.length; i++) {
            const change = prices[i] - prices[i - 1];
            if (change > 0) {
                gains += change;
            } else {
                losses += Math.abs(change);
            }
        }

        const avgGain = gains / period;
        const avgLoss = losses / period;
        
        if (avgLoss === 0) return 100;
        
        const rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }

    // Calculate MACD for trend
    calculateMACD(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        if (data.length < slowPeriod) return { macd: 0, signal: 0, histogram: 0 };

        const prices = data.map(d => d.close);
        const fastEMA = this.calculateEMA(prices, fastPeriod);
        const slowEMA = this.calculateEMA(prices, slowPeriod);
        
        const macdLine = fastEMA - slowEMA;
        
        // For simplicity, using SMA instead of EMA for signal line
        const macdHistory = data.slice(-signalPeriod).map(() => macdLine);
        const signalLine = macdHistory.reduce((sum, val) => sum + val, 0) / signalPeriod;
        
        return {
            macd: macdLine,
            signal: signalLine,
            histogram: macdLine - signalLine
        };
    }

    // Calculate EMA
    calculateEMA(prices, period) {
        if (prices.length < period) return prices[prices.length - 1];
        
        const multiplier = 2 / (period + 1);
        let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;
        
        for (let i = period; i < prices.length; i++) {
            ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
        }
        
        return ema;
    }

    // Check if price is near support/resistance
    isNearLevel(currentPrice, levels, tolerance = 0.003) {
        return levels.find(level => 
            Math.abs(currentPrice - level.price) / currentPrice <= tolerance
        );
    }

    // Generate scalping signal
    generateScalpingSignal(currentPrice, historicalData, prediction) {
        const rsi = this.calculateRSI(historicalData);
        const macd = this.calculateMACD(historicalData);
        
        // Update support/resistance levels
        this.calculateSupportResistance(historicalData);
        
        const nearSupport = this.isNearLevel(currentPrice, this.supportLevels);
        const nearResistance = this.isNearLevel(currentPrice, this.resistanceLevels);
        
        // Volume analysis
        const recentVolumes = historicalData.slice(-10).map(d => d.volume || 0);
        const avgVolume = recentVolumes.reduce((sum, v) => sum + v, 0) / recentVolumes.length;
        const currentVolume = historicalData[historicalData.length - 1]?.volume || 0;
        const volumeSpike = currentVolume > avgVolume * this.scalingSettings.volumeThreshold;

        // Scalping conditions
        const signals = {
            lstm: prediction ? this.getLSTMSignal(prediction, currentPrice) : 'hold',
            rsi: this.getRSISignal(rsi),
            macd: this.getMACDSignal(macd),
            support: nearSupport ? 'buy' : null,
            resistance: nearResistance ? 'sell' : null,
            volume: volumeSpike
        };

        return this.combineSignals(signals, currentPrice, {
            rsi,
            macd,
            nearSupport,
            nearResistance,
            volumeSpike
        });
    }

    // Get LSTM signal interpretation
    getLSTMSignal(prediction, currentPrice) {
        const priceChange = (prediction.predictedPrice - currentPrice) / currentPrice;
        if (priceChange > this.scalingSettings.quickProfitThreshold) return 'buy';
        if (priceChange < -this.scalingSettings.quickProfitThreshold) return 'sell';
        return 'hold';
    }

    // Get RSI signal
    getRSISignal(rsi) {
        if (rsi < 30) return 'buy';  // Oversold
        if (rsi > 70) return 'sell'; // Overbought
        return 'hold';
    }

    // Get MACD signal
    getMACDSignal(macd) {
        if (macd.macd > macd.signal && macd.histogram > 0) return 'buy';
        if (macd.macd < macd.signal && macd.histogram < 0) return 'sell';
        return 'hold';
    }

    // Combine all signals for final decision
    combineSignals(signals, currentPrice, indicators) {
        let buyScore = 0;
        let sellScore = 0;
        let confidence = 0;

        // LSTM weight: 40%
        if (signals.lstm === 'buy') buyScore += 4;
        if (signals.lstm === 'sell') sellScore += 4;

        // Support/Resistance weight: 30%
        if (signals.support) buyScore += 3;
        if (signals.resistance) sellScore += 3;

        // RSI weight: 15%
        if (signals.rsi === 'buy') buyScore += 1.5;
        if (signals.rsi === 'sell') sellScore += 1.5;

        // MACD weight: 15%
        if (signals.macd === 'buy') buyScore += 1.5;
        if (signals.macd === 'sell') sellScore += 1.5;

        // Volume confirmation
        if (signals.volume) {
            buyScore *= 1.2;
            sellScore *= 1.2;
            confidence += 0.1;
        }

        // Calculate confidence
        confidence += Math.abs(buyScore - sellScore) / 10;
        confidence = Math.min(confidence, 1);

        // Determine final signal
        let finalSignal = 'hold';
        let signalType = 'normal';

        if (buyScore > sellScore + 2) {
            finalSignal = 'buy';
            if (buyScore > 6) signalType = 'scalp'; // Strong signal for scalping
        } else if (sellScore > buyScore + 2) {
            finalSignal = 'sell';
            if (sellScore > 6) signalType = 'scalp';
        }

        return {
            signal: finalSignal,
            type: signalType,
            confidence: confidence,
            buyScore: buyScore,
            sellScore: sellScore,
            indicators: indicators,
            levels: {
                supports: this.supportLevels,
                resistances: this.resistanceLevels
            }
        };
    }

    // Get current analysis summary
    getAnalysisSummary(currentPrice) {
        return {
            supportLevels: this.supportLevels,
            resistanceLevels: this.resistanceLevels,
            nearestSupport: this.findNearestLevel(currentPrice, this.supportLevels),
            nearestResistance: this.findNearestLevel(currentPrice, this.resistanceLevels),
            scalingSettings: this.scalingSettings
        };
    }

    // Find nearest support/resistance level
    findNearestLevel(currentPrice, levels) {
        if (levels.length === 0) return null;
        
        return levels.reduce((nearest, level) => {
            const currentDistance = Math.abs(currentPrice - level.price);
            const nearestDistance = Math.abs(currentPrice - nearest.price);
            return currentDistance < nearestDistance ? level : nearest;
        });
    }
}

module.exports = TechnicalAnalysis;
