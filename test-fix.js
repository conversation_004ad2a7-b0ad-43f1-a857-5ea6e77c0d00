// Test script to verify the model fixes
const LSTMPredictor = require('./lstm-model.js');

async function testModelFix() {
    console.log('Testing LSTM Model Fixes...\n');
    
    // Create a new predictor instance
    const predictor = new LSTMPredictor();
    
    // Reset the model to start fresh
    console.log('1. Resetting model...');
    predictor.resetModel();
    
    // Create some sample data with current BTC price range
    const currentPrice = 1727000000; // Current BTC price in IDR
    const sampleData = [];
    
    // Generate 50 sample candles around current price
    for (let i = 0; i < 50; i++) {
        const basePrice = currentPrice + (Math.random() - 0.5) * 50000000; // ±50M variation
        const high = basePrice + Math.random() * 10000000;
        const low = basePrice - Math.random() * 10000000;
        const close = low + Math.random() * (high - low);
        
        sampleData.push({
            timestamp: Date.now() - (50 - i) * 60000, // 1 minute intervals
            open: basePrice,
            high: high,
            low: low,
            close: close,
            volume: Math.random() * 10
        });
    }
    
    console.log('2. Generated sample data with current price range');
    console.log(`   Price range: ${Math.min(...sampleData.map(d => d.close)).toLocaleString('id-ID')} - ${Math.max(...sampleData.map(d => d.close)).toLocaleString('id-ID')} IDR`);
    
    // Train the model
    console.log('\n3. Training model...');
    const trainSuccess = await predictor.trainModel(sampleData, 50); // Quick training
    console.log(`   Training result: ${trainSuccess ? 'Success' : 'Failed'}`);
    
    // Make a prediction
    console.log('\n4. Making prediction...');
    const prediction = await predictor.predict(sampleData);
    
    if (prediction) {
        console.log(`   Current Price: ${prediction.currentPrice.toLocaleString('id-ID')} IDR`);
        console.log(`   Predicted Price: ${prediction.predictedPrice.toLocaleString('id-ID')} IDR`);
        console.log(`   Confidence: ${(prediction.confidence * 100).toFixed(1)}%`);
        
        // Generate signal
        console.log('\n5. Generating trading signal...');
        const signal = predictor.generateSignal(prediction, prediction.currentPrice);
        console.log(`   Signal: ${signal.toUpperCase()}`);
        
        // Test with different thresholds
        console.log('\n6. Testing different thresholds:');
        [0.01, 0.03, 0.05, 0.10].forEach(threshold => {
            const testSignal = predictor.generateSignal(prediction, prediction.currentPrice, threshold);
            console.log(`   Threshold ${(threshold * 100).toFixed(0)}%: ${testSignal.toUpperCase()}`);
        });
        
    } else {
        console.log('   Prediction failed!');
    }
    
    console.log('\n7. Model info:');
    const modelInfo = predictor.getModelInfo();
    console.log(`   Status: ${modelInfo.modelStatus}`);
    console.log(`   Learning Stats: ${JSON.stringify(modelInfo.learningStats, null, 2)}`);
    
    console.log('\nTest completed!');
}

// Run the test
testModelFix().catch(console.error);
