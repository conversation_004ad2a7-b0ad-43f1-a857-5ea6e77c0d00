// Trading Bot Dashboard JavaScript

class TradingBotDashboard {
    constructor() {
        this.chart = null;
        this.assetChart = null;
        this.priceData = [];
        this.predictionData = [];
        this.assetHistory = [];
        this.isRunning = false;
        this.updateInterval = null;
        this.previousPrice = 0;
        this.startingPrice = 0;
        this.startingAssetValue = 0;

        this.initializeChart();
        this.initializeAssetChart();
        this.bindEvents();
        this.startDataUpdates();
    }

    // Initialize Chart.js
    initializeChart() {
        const ctx = document.getElementById('priceChart').getContext('2d');

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Actual Price',
                        data: [],
                        borderColor: '#00d4aa',
                        backgroundColor: 'rgba(0, 212, 170, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        pointBackgroundColor: '#00d4aa',
                        pointBorderColor: '#00d4aa'
                    },
                    {
                        label: 'Predicted Price',
                        data: [],
                        borderColor: '#4dabf7',
                        backgroundColor: 'rgba(77, 171, 247, 0.1)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        pointBackgroundColor: '#4dabf7',
                        pointBorderColor: '#4dabf7',
                        spanGaps: true // This allows gaps in the prediction line
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'BTC/IDR Price & Predictions',
                        color: '#ffffff',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: true,
                        labels: {
                            color: '#ffffff',
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#333',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                if (context.parsed.y !== null) {
                                    return context.dataset.label + ': Rp ' +
                                           context.parsed.y.toLocaleString('id-ID');
                                }
                                return null;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Time',
                            color: '#999'
                        },
                        ticks: {
                            color: '#999',
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: '#333',
                            drawBorder: false
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Price (IDR)',
                            color: '#999'
                        },
                        ticks: {
                            color: '#999',
                            callback: function(value) {
                                return 'Rp ' + (value / 1000000).toFixed(0) + 'M';
                            }
                        },
                        grid: {
                            color: '#333',
                            drawBorder: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                }
            }
        });
    }

    // Initialize Asset Growth Chart
    initializeAssetChart() {
        const ctx = document.getElementById('assetChart').getContext('2d');

        this.assetChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Total Asset Value',
                        data: [],
                        borderColor: '#ffd700',
                        backgroundColor: 'rgba(255, 215, 0, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.2,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointBackgroundColor: '#ffd700',
                        pointBorderColor: '#ffd700',
                        pointBorderWidth: 2
                    },
                    {
                        label: 'Starting Value',
                        data: [],
                        borderColor: '#666',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false,
                        pointRadius: 0,
                        pointHoverRadius: 0
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        display: true,
                        labels: {
                            color: '#ffffff',
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#333',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                if (context.parsed.y !== null) {
                                    const value = context.parsed.y;
                                    return context.dataset.label + ': Rp ' +
                                           value.toLocaleString('id-ID');
                                }
                                return null;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Time',
                            color: '#999'
                        },
                        ticks: {
                            color: '#999',
                            maxTicksLimit: 8
                        },
                        grid: {
                            color: '#333',
                            drawBorder: false
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Asset Value (IDR)',
                            color: '#999'
                        },
                        ticks: {
                            color: '#999',
                            callback: function(value) {
                                return 'Rp ' + (value / 1000000).toFixed(1) + 'M';
                            }
                        },
                        grid: {
                            color: '#333',
                            drawBorder: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                }
            }
        });
    }

    // Bind event listeners
    bindEvents() {
        document.getElementById('startBtn').addEventListener('click', () => this.startBot());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopBot());
        document.getElementById('retrainBtn').addEventListener('click', () => this.retrainModel());
        document.getElementById('saveModelBtn').addEventListener('click', () => this.saveModel());
        document.getElementById('loadModelBtn').addEventListener('click', () => this.loadModel());
        document.getElementById('resetModelBtn').addEventListener('click', () => this.resetModel());
        document.getElementById('toggleScalpingBtn').addEventListener('click', () => this.toggleScalping());

        // Handle window resize
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
            if (this.assetChart) {
                this.assetChart.resize();
            }
        });
    }

    // Start bot
    async startBot() {
        try {
            const response = await fetch('/api/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ isTestMode: true })
            });
            
            const result = await response.json();
            this.showNotification(result.status, 'success');
            
            // Update button states
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            
        } catch (error) {
            this.showNotification('Failed to start bot: ' + error.message, 'error');
        }
    }

    // Stop bot
    async stopBot() {
        try {
            const response = await fetch('/api/stop', {
                method: 'POST'
            });
            
            const result = await response.json();
            this.showNotification(result.status, 'success');
            
            // Update button states
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
        } catch (error) {
            this.showNotification('Failed to stop bot: ' + error.message, 'error');
        }
    }

    // Retrain model
    async retrainModel() {
        try {
            const response = await fetch('/api/retrain', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.status, response.ok ? 'success' : 'error');

        } catch (error) {
            this.showNotification('Failed to retrain model: ' + error.message, 'error');
        }
    }

    // Save model
    async saveModel() {
        try {
            const response = await fetch('/api/save-model', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.status, response.ok ? 'success' : 'error');

            if (response.ok) {
                document.getElementById('lastSaved').textContent = new Date().toLocaleString();
            }

        } catch (error) {
            this.showNotification('Failed to save model: ' + error.message, 'error');
        }
    }

    // Load model
    async loadModel() {
        try {
            const response = await fetch('/api/load-model', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.status, response.ok ? 'success' : 'error');

        } catch (error) {
            this.showNotification('Failed to load model: ' + error.message, 'error');
        }
    }

    // Reset model
    async resetModel() {
        try {
            // Confirm with user
            if (!confirm('Are you sure you want to reset the model? This will delete all training data and start fresh.')) {
                return;
            }

            const response = await fetch('/api/reset-model', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.message, response.ok ? 'success' : 'error');

            // Refresh model info after reset
            if (response.ok) {
                setTimeout(() => this.fetchBotData(), 1000);
            }

        } catch (error) {
            this.showNotification('Failed to reset model: ' + error.message, 'error');
        }
    }

    // Toggle scalping mode
    async toggleScalping() {
        try {
            const response = await fetch('/api/toggle-scalping', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.message, response.ok ? 'success' : 'error');

            // Update UI immediately
            if (response.ok) {
                this.updateScalpingStatus(result.scalpingEnabled);
            }

        } catch (error) {
            this.showNotification('Failed to toggle scalping: ' + error.message, 'error');
        }
    }

    // Fetch bot data
    async fetchBotData() {
        try {
            const response = await fetch('/api/data');
            const data = await response.json();
            
            this.updateUI(data);
            
            // Also fetch model info
            const modelResponse = await fetch('/api/model-info');
            const modelData = await modelResponse.json();
            this.updateModelInfo(modelData);

            // Update scalping stats if available in main data
            if (data.scalpingStats) {
                this.updateScalpingInfo(data.scalpingStats);
            } else {
                // Fetch scalping stats separately if not in main data
                this.fetchScalpingStats();
            }

            // Fetch technical analysis
            this.fetchTechnicalAnalysis();
            
        } catch (error) {
            console.error('Failed to fetch bot data:', error);
        }
    }

    // Update UI with bot data
    updateUI(data) {
        // Update status
        document.getElementById('botStatus').textContent = data.status;
        const statusIndicator = document.getElementById('statusIndicator');
        
        if (data.isRunning) {
            statusIndicator.className = 'w-4 h-4 bg-crypto-green rounded-full status-running';
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
        } else {
            statusIndicator.className = 'w-4 h-4 bg-gray-500 rounded-full';
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }

        // Update prices
        document.getElementById('currentPrice').textContent = `Rp ${data.lastPrice.toLocaleString('id-ID')}`;
        document.getElementById('predictedPrice').textContent = `Rp ${data.predictedPrice.toLocaleString('id-ID')}`;
        document.getElementById('confidence').textContent = `${(data.predictionConfidence * 100).toFixed(1)}%`;

        // Calculate and update price changes
        this.updatePriceChanges(data);

        // Update trading signal
        const signalElement = document.getElementById('tradingSignal');
        signalElement.textContent = data.lastSignal.toUpperCase();
        
        // Color code the signal
        signalElement.className = 'text-3xl font-bold mb-2';
        if (data.lastSignal === 'buy') {
            signalElement.classList.add('text-crypto-green');
        } else if (data.lastSignal === 'sell') {
            signalElement.classList.add('text-crypto-red');
        } else {
            signalElement.classList.add('text-gray-300');
        }

        // Update portfolio
        document.getElementById('idrBalance').textContent = `Rp ${data.balanceIdr.toLocaleString('id-ID')}`;
        document.getElementById('btcBalance').textContent = `${data.balanceBtc.toFixed(8)} BTC`;
        document.getElementById('tradingMode').textContent = data.isTestMode ? 'Test' : 'Live';

        // Calculate and display total asset value
        const btcValueInIdr = data.balanceBtc * data.lastPrice;
        const totalAssetValue = data.balanceIdr + btcValueInIdr;
        document.getElementById('totalAsset').textContent = `Rp ${totalAssetValue.toLocaleString('id-ID')}`;

        // Track asset growth
        this.trackAssetGrowth(totalAssetValue, data.timestamp || Date.now());

        // Update trade log
        this.updateTradeLog(data.tradeLog);

        // Update scalping status
        if (data.scalpingEnabled !== undefined) {
            this.updateScalpingStatus(data.scalpingEnabled);
        }

        // Update scalping stats
        if (data.scalpingStats) {
            this.updateScalpingInfo(data.scalpingStats);
        }

        // Update chart
        this.updateChart(data);
    }

    // Calculate and update price changes
    updatePriceChanges(data) {
        const currentPrice = data.lastPrice;
        const predictedPrice = data.predictedPrice;

        // Set starting price on first update
        if (this.startingPrice === 0) {
            this.startingPrice = currentPrice;
        }

        // Calculate 24h change (simulated as change from starting price)
        const change24h = this.startingPrice > 0 ? ((currentPrice - this.startingPrice) / this.startingPrice) * 100 : 0;

        // Calculate predicted change
        const predictedChange = currentPrice > 0 ? ((predictedPrice - currentPrice) / currentPrice) * 100 : 0;

        // Calculate real-time price movement (from previous price)
        let priceMovement = 0;
        let movementDirection = 'No change';
        let movementIcon = '📊';
        let movementColor = 'text-gray-300';

        if (this.previousPrice > 0) {
            priceMovement = ((currentPrice - this.previousPrice) / this.previousPrice) * 100;

            if (priceMovement > 0) {
                movementDirection = 'Increasing';
                movementIcon = '📈';
                movementColor = 'text-crypto-green';
            } else if (priceMovement < 0) {
                movementDirection = 'Decreasing';
                movementIcon = '📉';
                movementColor = 'text-crypto-red';
            }
        }

        // Update 24h change display
        const change24hElement = document.getElementById('priceChange24h');
        if (change24hElement) {
            change24hElement.textContent = `${change24h >= 0 ? '+' : ''}${change24h.toFixed(2)}%`;
            change24hElement.className = `text-sm font-semibold ${change24h >= 0 ? 'text-crypto-green' : 'text-crypto-red'}`;
        }

        // Update predicted change display
        const predictedChangeElement = document.getElementById('predictedChange');
        if (predictedChangeElement) {
            predictedChangeElement.textContent = `${predictedChange >= 0 ? '+' : ''}${predictedChange.toFixed(2)}%`;
            predictedChangeElement.className = `text-sm font-semibold ${predictedChange >= 0 ? 'text-crypto-green' : 'text-crypto-red'}`;
        }

        // Update price movement display
        const priceMovementElement = document.getElementById('priceMovement');
        const priceDirectionElement = document.getElementById('priceDirection');
        const movementIconElement = document.getElementById('movementIcon');

        if (priceMovementElement) {
            priceMovementElement.textContent = `${priceMovement >= 0 ? '+' : ''}${priceMovement.toFixed(3)}%`;
            priceMovementElement.className = `text-xl font-bold ${movementColor}`;
        }

        if (priceDirectionElement) {
            priceDirectionElement.textContent = movementDirection;
        }

        if (movementIconElement) {
            movementIconElement.textContent = movementIcon;
        }

        // Store current price for next comparison
        this.previousPrice = currentPrice;
    }

    // Update model information
    updateModelInfo(modelData) {
        document.getElementById('onlineLearning').textContent = modelData.onlineLearning ? 'Online' : 'Offline';

        if (modelData.learningStats) {
            document.getElementById('modelAccuracy').textContent = `${(modelData.learningStats.accuracy * 100).toFixed(1)}%`;
            document.getElementById('totalPredictions').textContent = modelData.learningStats.totalPredictions;
        }

        // Update model status
        if (modelData.modelStatus) {
            document.getElementById('modelStatus').textContent = modelData.modelStatus;
        }

        if (modelData.lastSaved) {
            document.getElementById('lastSaved').textContent = new Date(modelData.lastSaved).toLocaleString();
        }
    }

    // Update trade log
    updateTradeLog(tradeLog) {
        const logContainer = document.getElementById('tradeLog');
        
        if (tradeLog && tradeLog.length > 0) {
            logContainer.innerHTML = tradeLog.slice(0, 10).map(log => 
                `<div class="text-sm text-gray-300 p-2 bg-black bg-opacity-20 rounded">${log}</div>`
            ).join('');
        } else {
            logContainer.innerHTML = '<p class="text-gray-400 text-center py-4">No trades yet...</p>';
        }
    }

    // Update chart with new data
    updateChart(data) {
        if (!this.chart || !data.historicalData) return;

        // Prepare data for chart
        const times = [];
        const prices = [];
        const predictions = [];

        // Get last 50 data points for chart
        const recentData = data.historicalData.slice(-50);
        const recentPredictions = data.predictionHistory ? data.predictionHistory.slice(-50) : [];

        // Create a map of prediction timestamps to prices for quick lookup
        const predictionMap = new Map();
        recentPredictions.forEach(pred => {
            predictionMap.set(pred.timestamp, pred.price);
        });

        recentData.forEach((item, index) => {
            const time = new Date(item.timestamp).toLocaleTimeString();
            times.push(time);
            prices.push(item.close);

            // Look for a prediction close to this timestamp (within 30 seconds)
            let predictionPrice = null;
            for (const [predTimestamp, predPrice] of predictionMap) {
                if (Math.abs(predTimestamp - item.timestamp) < 30000) { // 30 seconds tolerance
                    predictionPrice = predPrice;
                    break;
                }
            }

            predictions.push(predictionPrice);
        });

        // If we have recent predictions but no matches, add them to the end
        if (recentPredictions.length > 0 && predictions.every(p => p === null)) {
            // Add the most recent predictions to the end of the chart
            const lastPredictions = recentPredictions.slice(-10); // Last 10 predictions
            lastPredictions.forEach((pred, index) => {
                if (index < predictions.length) {
                    predictions[predictions.length - lastPredictions.length + index] = pred.price;
                }
            });
        }

        // Fallback: If we still have no predictions but have current predicted price, add it to the last point
        if (predictions.every(p => p === null) && data.predictedPrice > 0) {
            predictions[predictions.length - 1] = data.predictedPrice;
            console.log('Using fallback: added current predicted price to last point');
        }

        // Alternative approach: Create a separate prediction line with timestamps
        const predictionLine = [];
        if (recentPredictions.length > 0) {
            // Map predictions to chart points based on their timestamps
            recentData.forEach((item, index) => {
                // Find the closest prediction in time
                let closestPrediction = null;
                let minTimeDiff = Infinity;

                recentPredictions.forEach(pred => {
                    const timeDiff = Math.abs(pred.timestamp - item.timestamp);
                    if (timeDiff < minTimeDiff) {
                        minTimeDiff = timeDiff;
                        closestPrediction = pred;
                    }
                });

                // If prediction is within 60 seconds, use it
                if (closestPrediction && minTimeDiff < 60000) {
                    predictionLine.push(closestPrediction.price);
                } else {
                    predictionLine.push(null);
                }
            });

            // If predictionLine has more valid points, use it instead
            const validPredictionPoints = predictionLine.filter(p => p !== null).length;
            const validOriginalPoints = predictions.filter(p => p !== null).length;

            if (validPredictionPoints > validOriginalPoints) {
                console.log('Using alternative prediction mapping with', validPredictionPoints, 'points');
                predictions.splice(0, predictions.length, ...predictionLine);
            }
        }

        console.log('Chart update:', {
            dataPoints: recentData.length,
            predictionPoints: recentPredictions.length,
            predictionsWithValues: predictions.filter(p => p !== null).length,
            predictionHistory: data.predictionHistory ? data.predictionHistory.length : 'undefined',
            samplePredictions: recentPredictions.slice(0, 3)
        });

        // Update Chart.js chart
        this.chart.data.labels = times;
        this.chart.data.datasets[0].data = prices;
        this.chart.data.datasets[1].data = predictions;
        this.chart.update('none'); // 'none' for no animation for better performance
    }

    // Track asset growth over time
    trackAssetGrowth(totalAssetValue, timestamp) {
        // Set starting value on first update
        if (this.startingAssetValue === 0) {
            this.startingAssetValue = totalAssetValue;
            document.getElementById('startingAssetValue').textContent = `Rp ${totalAssetValue.toLocaleString('id-ID')}`;
        }

        // Add to asset history
        this.assetHistory.push({
            timestamp: timestamp,
            value: totalAssetValue
        });

        // Keep only last 100 data points for performance
        if (this.assetHistory.length > 100) {
            this.assetHistory.shift();
        }

        // Update asset chart
        this.updateAssetChart();

        // Update asset growth statistics
        this.updateAssetGrowthStats(totalAssetValue);
    }

    // Update asset growth chart
    updateAssetChart() {
        if (!this.assetChart || this.assetHistory.length === 0) return;

        // Prepare data for chart
        const times = [];
        const values = [];
        const startingValues = [];

        // Get recent asset history
        const recentHistory = this.assetHistory.slice(-50); // Last 50 points

        recentHistory.forEach(item => {
            const time = new Date(item.timestamp).toLocaleTimeString();
            times.push(time);
            values.push(item.value);
            startingValues.push(this.startingAssetValue); // Baseline reference
        });

        // Update Chart.js asset chart
        this.assetChart.data.labels = times;
        this.assetChart.data.datasets[0].data = values;
        this.assetChart.data.datasets[1].data = startingValues;
        this.assetChart.update('none');
    }

    // Update asset growth statistics
    updateAssetGrowthStats(currentValue) {
        // Update current asset value display
        document.getElementById('currentAssetValue').textContent = `Rp ${currentValue.toLocaleString('id-ID')}`;

        // Calculate and display growth percentage
        if (this.startingAssetValue > 0) {
            const growthPercent = ((currentValue - this.startingAssetValue) / this.startingAssetValue) * 100;
            const growthElement = document.getElementById('totalGrowth');

            growthElement.textContent = `${growthPercent >= 0 ? '+' : ''}${growthPercent.toFixed(2)}%`;
            growthElement.className = `text-lg font-semibold ${
                growthPercent >= 0 ? 'text-crypto-green' : 'text-crypto-red'
            }`;
        }
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 transition-all duration-300 ${
            type === 'success' ? 'bg-crypto-green' : 
            type === 'error' ? 'bg-crypto-red' : 'bg-crypto-blue'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Start periodic data updates
    startDataUpdates() {
        // Initial fetch
        this.fetchBotData();
        
        // Update every 2 seconds
        this.updateInterval = setInterval(() => {
            this.fetchBotData();
        }, 2000);
    }

    // Stop data updates
    stopDataUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    // Fetch scalping statistics
    async fetchScalpingStats() {
        try {
            const response = await fetch('/api/scalping-stats');
            const data = await response.json();
            this.updateScalpingInfo(data);
        } catch (error) {
            console.error('Failed to fetch scalping stats:', error);
        }
    }

    // Fetch technical analysis data
    async fetchTechnicalAnalysis() {
        try {
            const response = await fetch('/api/technical-analysis');
            const data = await response.json();
            this.updateTechnicalAnalysis(data);
        } catch (error) {
            console.error('Failed to fetch technical analysis:', error);
        }
    }

    // Update scalping information
    updateScalpingInfo(data) {
        console.log('Updating scalping info:', data); // Debug log

        // Update active trades count
        if (data.activeTrades !== undefined) {
            const activeCount = Array.isArray(data.activeTrades) ? data.activeTrades.length : 0;
            document.getElementById('activeScalpTrades').textContent = activeCount;
        }

        // Update profit statistics
        if (data.profitStats) {
            const stats = data.profitStats;

            // Win rate
            const winRate = stats.winRate || 0;
            document.getElementById('scalpWinRate').textContent = `${winRate.toFixed(1)}%`;

            // Total profit calculation
            const totalProfit = (stats.totalProfit || 0) - (stats.totalLoss || 0);
            const profitElement = document.getElementById('scalpTotalProfit');

            if (profitElement) {
                profitElement.textContent = `Rp ${totalProfit.toLocaleString('id-ID')}`;
                profitElement.className = totalProfit >= 0 ? 'text-crypto-green font-semibold' : 'text-crypto-red font-semibold';
            }

            console.log(`Scalping stats updated: Active: ${data.activeTrades?.length || 0}, Win Rate: ${winRate.toFixed(1)}%, Profit: ${totalProfit.toLocaleString('id-ID')}`);
        }
    }

    // Update scalping status
    updateScalpingStatus(enabled) {
        const statusElement = document.getElementById('scalpingStatus');
        statusElement.textContent = enabled ? 'Enabled' : 'Disabled';
        statusElement.className = enabled ? 'text-crypto-green font-semibold' : 'text-crypto-red font-semibold';
    }

    // Update technical analysis information
    updateTechnicalAnalysis(data) {
        if (data.currentIndicators) {
            document.getElementById('rsiValue').textContent = data.currentIndicators.rsi.toFixed(1);
            document.getElementById('macdValue').textContent = data.currentIndicators.macd.macd.toFixed(4);
        }

        if (data.supportLevels) {
            document.getElementById('supportCount').textContent = data.supportLevels.length;
        }

        if (data.resistanceLevels) {
            document.getElementById('resistanceCount').textContent = data.resistanceLevels.length;
        }

        if (data.enhancedSignal) {
            const signalElement = document.getElementById('enhancedSignal');
            signalElement.textContent = data.enhancedSignal.signal.toUpperCase();

            // Color code the enhanced signal
            signalElement.className = 'font-semibold';
            if (data.enhancedSignal.signal === 'buy') {
                signalElement.classList.add('text-crypto-green');
            } else if (data.enhancedSignal.signal === 'sell') {
                signalElement.classList.add('text-crypto-red');
            } else {
                signalElement.classList.add('text-gray-300');
            }
        }
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TradingBotDashboard();
});
