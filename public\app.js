// Trading Bot Dashboard JavaScript

class TradingBotDashboard {
    constructor() {
        this.chart = null;
        this.priceData = [];
        this.predictionData = [];
        this.isRunning = false;
        this.updateInterval = null;
        this.previousPrice = 0;
        this.startingPrice = 0;
        
        this.initializeChart();
        this.bindEvents();
        this.startDataUpdates();
    }

    // Initialize ECharts
    initializeChart() {
        const chartDom = document.getElementById('priceChart');
        this.chart = echarts.init(chartDom);
        
        const option = {
            backgroundColor: 'transparent',
            title: {
                text: 'BTC/IDR Price & Predictions',
                textStyle: {
                    color: '#ffffff',
                    fontSize: 16
                }
            },
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.8)',
                borderColor: '#333',
                textStyle: {
                    color: '#fff'
                },
                formatter: function (params) {
                    let result = params[0].name + '<br/>';
                    params.forEach(function (item) {
                        if (item.value !== null) {
                            result += item.marker + ' ' + item.seriesName + ': Rp ' +
                                     item.value.toLocaleString('id-ID') + '<br/>';
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: ['Actual Price', 'Predicted Price'],
                textStyle: {
                    color: '#ffffff'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ffffff'
                    }
                },
                axisLabel: {
                    color: '#ffffff'
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: {
                        color: '#ffffff'
                    }
                },
                axisLabel: {
                    color: '#ffffff',
                    formatter: 'Rp {value}'
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,0.1)'
                    }
                }
            },
              toolbox: {
    feature: {
      dataZoom: {
        yAxisIndex: 'none'
      },
      restore: {},
      saveAsImage: {}
    }
  },
            series: [
                {
                    name: 'Actual Price',
                    type: 'line',
                    data: [],
                    lineStyle: {
                        color: '#00d4aa',
                        width: 2
                    },
                    itemStyle: {
                        color: '#00d4aa'
                    }
                },
                {
                    name: 'Predicted Price',
                    type: 'line',
                    data: [],
                    lineStyle: {
                        color: '#4dabf7',
                        width: 2,
                        type: 'dashed'
                    },
                    itemStyle: {
                        color: '#4dabf7'
                    }
                }
            ]
        };
        
        this.chart.setOption(option);
    }

    // Bind event listeners
    bindEvents() {
        document.getElementById('startBtn').addEventListener('click', () => this.startBot());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopBot());
        document.getElementById('retrainBtn').addEventListener('click', () => this.retrainModel());
        document.getElementById('saveModelBtn').addEventListener('click', () => this.saveModel());
        document.getElementById('loadModelBtn').addEventListener('click', () => this.loadModel());
        document.getElementById('resetModelBtn').addEventListener('click', () => this.resetModel());

        // Handle window resize
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });
    }

    // Start bot
    async startBot() {
        try {
            const response = await fetch('/api/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ isTestMode: true })
            });
            
            const result = await response.json();
            this.showNotification(result.status, 'success');
            
            // Update button states
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            
        } catch (error) {
            this.showNotification('Failed to start bot: ' + error.message, 'error');
        }
    }

    // Stop bot
    async stopBot() {
        try {
            const response = await fetch('/api/stop', {
                method: 'POST'
            });
            
            const result = await response.json();
            this.showNotification(result.status, 'success');
            
            // Update button states
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
        } catch (error) {
            this.showNotification('Failed to stop bot: ' + error.message, 'error');
        }
    }

    // Retrain model
    async retrainModel() {
        try {
            const response = await fetch('/api/retrain', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.status, response.ok ? 'success' : 'error');

        } catch (error) {
            this.showNotification('Failed to retrain model: ' + error.message, 'error');
        }
    }

    // Save model
    async saveModel() {
        try {
            const response = await fetch('/api/save-model', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.status, response.ok ? 'success' : 'error');

            if (response.ok) {
                document.getElementById('lastSaved').textContent = new Date().toLocaleString();
            }

        } catch (error) {
            this.showNotification('Failed to save model: ' + error.message, 'error');
        }
    }

    // Load model
    async loadModel() {
        try {
            const response = await fetch('/api/load-model', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.status, response.ok ? 'success' : 'error');

        } catch (error) {
            this.showNotification('Failed to load model: ' + error.message, 'error');
        }
    }

    // Reset model
    async resetModel() {
        try {
            // Confirm with user
            if (!confirm('Are you sure you want to reset the model? This will delete all training data and start fresh.')) {
                return;
            }

            const response = await fetch('/api/reset-model', {
                method: 'POST'
            });

            const result = await response.json();
            this.showNotification(result.message, response.ok ? 'success' : 'error');

            // Refresh model info after reset
            if (response.ok) {
                setTimeout(() => this.fetchBotData(), 1000);
            }

        } catch (error) {
            this.showNotification('Failed to reset model: ' + error.message, 'error');
        }
    }

    // Fetch bot data
    async fetchBotData() {
        try {
            const response = await fetch('/api/data');
            const data = await response.json();
            
            this.updateUI(data);
            
            // Also fetch model info
            const modelResponse = await fetch('/api/model-info');
            const modelData = await modelResponse.json();
            this.updateModelInfo(modelData);
            
        } catch (error) {
            console.error('Failed to fetch bot data:', error);
        }
    }

    // Update UI with bot data
    updateUI(data) {
        // Update status
        document.getElementById('botStatus').textContent = data.status;
        const statusIndicator = document.getElementById('statusIndicator');
        
        if (data.isRunning) {
            statusIndicator.className = 'w-4 h-4 bg-crypto-green rounded-full status-running';
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
        } else {
            statusIndicator.className = 'w-4 h-4 bg-gray-500 rounded-full';
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }

        // Update prices
        document.getElementById('currentPrice').textContent = `Rp ${data.lastPrice.toLocaleString('id-ID')}`;
        document.getElementById('predictedPrice').textContent = `Rp ${data.predictedPrice.toLocaleString('id-ID')}`;
        document.getElementById('confidence').textContent = `${(data.predictionConfidence * 100).toFixed(1)}%`;

        // Calculate and update price changes
        this.updatePriceChanges(data);

        // Update trading signal
        const signalElement = document.getElementById('tradingSignal');
        signalElement.textContent = data.lastSignal.toUpperCase();
        
        // Color code the signal
        signalElement.className = 'text-3xl font-bold mb-2';
        if (data.lastSignal === 'buy') {
            signalElement.classList.add('text-crypto-green');
        } else if (data.lastSignal === 'sell') {
            signalElement.classList.add('text-crypto-red');
        } else {
            signalElement.classList.add('text-gray-300');
        }

        // Update portfolio
        document.getElementById('idrBalance').textContent = `Rp ${data.balanceIdr.toLocaleString('id-ID')}`;
        document.getElementById('btcBalance').textContent = `${data.balanceBtc.toFixed(8)} BTC`;
        document.getElementById('tradingMode').textContent = data.isTestMode ? 'Test' : 'Live';

        // Calculate and display total asset value
        const btcValueInIdr = data.balanceBtc * data.lastPrice;
        const totalAssetValue = data.balanceIdr + btcValueInIdr;
        document.getElementById('totalAsset').textContent = `Rp ${totalAssetValue.toLocaleString('id-ID')}`;

        // Update trade log
        this.updateTradeLog(data.tradeLog);

        // Update chart
        this.updateChart(data);
    }

    // Calculate and update price changes
    updatePriceChanges(data) {
        const currentPrice = data.lastPrice;
        const predictedPrice = data.predictedPrice;

        // Set starting price on first update
        if (this.startingPrice === 0) {
            this.startingPrice = currentPrice;
        }

        // Calculate 24h change (simulated as change from starting price)
        const change24h = this.startingPrice > 0 ? ((currentPrice - this.startingPrice) / this.startingPrice) * 100 : 0;

        // Calculate predicted change
        const predictedChange = currentPrice > 0 ? ((predictedPrice - currentPrice) / currentPrice) * 100 : 0;

        // Calculate real-time price movement (from previous price)
        let priceMovement = 0;
        let movementDirection = 'No change';
        let movementIcon = '📊';
        let movementColor = 'text-gray-300';

        if (this.previousPrice > 0) {
            priceMovement = ((currentPrice - this.previousPrice) / this.previousPrice) * 100;

            if (priceMovement > 0) {
                movementDirection = 'Increasing';
                movementIcon = '📈';
                movementColor = 'text-crypto-green';
            } else if (priceMovement < 0) {
                movementDirection = 'Decreasing';
                movementIcon = '📉';
                movementColor = 'text-crypto-red';
            }
        }

        // Update 24h change display
        const change24hElement = document.getElementById('priceChange24h');
        if (change24hElement) {
            change24hElement.textContent = `${change24h >= 0 ? '+' : ''}${change24h.toFixed(2)}%`;
            change24hElement.className = `text-sm font-semibold ${change24h >= 0 ? 'text-crypto-green' : 'text-crypto-red'}`;
        }

        // Update predicted change display
        const predictedChangeElement = document.getElementById('predictedChange');
        if (predictedChangeElement) {
            predictedChangeElement.textContent = `${predictedChange >= 0 ? '+' : ''}${predictedChange.toFixed(2)}%`;
            predictedChangeElement.className = `text-sm font-semibold ${predictedChange >= 0 ? 'text-crypto-green' : 'text-crypto-red'}`;
        }

        // Update price movement display
        const priceMovementElement = document.getElementById('priceMovement');
        const priceDirectionElement = document.getElementById('priceDirection');
        const movementIconElement = document.getElementById('movementIcon');

        if (priceMovementElement) {
            priceMovementElement.textContent = `${priceMovement >= 0 ? '+' : ''}${priceMovement.toFixed(3)}%`;
            priceMovementElement.className = `text-xl font-bold ${movementColor}`;
        }

        if (priceDirectionElement) {
            priceDirectionElement.textContent = movementDirection;
        }

        if (movementIconElement) {
            movementIconElement.textContent = movementIcon;
        }

        // Store current price for next comparison
        this.previousPrice = currentPrice;
    }

    // Update model information
    updateModelInfo(modelData) {
        document.getElementById('onlineLearning').textContent = modelData.onlineLearning ? 'Online' : 'Offline';

        if (modelData.learningStats) {
            document.getElementById('modelAccuracy').textContent = `${(modelData.learningStats.accuracy * 100).toFixed(1)}%`;
            document.getElementById('totalPredictions').textContent = modelData.learningStats.totalPredictions;
        }

        // Update model status
        if (modelData.modelStatus) {
            document.getElementById('modelStatus').textContent = modelData.modelStatus;
        }

        if (modelData.lastSaved) {
            document.getElementById('lastSaved').textContent = new Date(modelData.lastSaved).toLocaleString();
        }
    }

    // Update trade log
    updateTradeLog(tradeLog) {
        const logContainer = document.getElementById('tradeLog');
        
        if (tradeLog && tradeLog.length > 0) {
            logContainer.innerHTML = tradeLog.slice(0, 10).map(log => 
                `<div class="text-sm text-gray-300 p-2 bg-black bg-opacity-20 rounded">${log}</div>`
            ).join('');
        } else {
            logContainer.innerHTML = '<p class="text-gray-400 text-center py-4">No trades yet...</p>';
        }
    }

    // Update chart with new data
    updateChart(data) {
        if (!this.chart || !data.historicalData) return;

        // Prepare data for chart
        const times = [];
        const prices = [];
        const predictions = [];

        // Get last 50 data points for chart
        const recentData = data.historicalData.slice(-50);
        
        recentData.forEach((item, index) => {
            const time = new Date(item.timestamp).toLocaleTimeString();
            times.push(time);
            prices.push(item.close);
            
            // Add prediction for current point
            if (index === recentData.length - 1 && data.predictedPrice > 0) {
                predictions.push(data.predictedPrice);
            } else {
                predictions.push(null);
            }
        });

        // Update chart
        this.chart.setOption({
            xAxis: {
                data: times
            },
            series: [
                {
                    data: prices
                },
                {
                    data: predictions
                }
            ]
        });
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 transition-all duration-300 ${
            type === 'success' ? 'bg-crypto-green' : 
            type === 'error' ? 'bg-crypto-red' : 'bg-crypto-blue'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Start periodic data updates
    startDataUpdates() {
        // Initial fetch
        this.fetchBotData();
        
        // Update every 2 seconds
        this.updateInterval = setInterval(() => {
            this.fetchBotData();
        }, 2000);
    }

    // Stop data updates
    stopDataUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TradingBotDashboard();
});
