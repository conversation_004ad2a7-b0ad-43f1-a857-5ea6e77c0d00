// lstm-model.js
// Lightweight LSTM-inspired Neural Network for Price Prediction

const fs = require('fs');
const path = require('path');

class LSTMPredictor {
    constructor(sequenceLength = 20, features = 5) {
        this.sequenceLength = sequenceLength; // Number of time steps to look back
        this.features = features; // Number of features (open, high, low, close, volume)
        this.isTraining = false;
        this.lastPrediction = null;
        this.predictionConfidence = 0;
        this.scaler = {
            min: null,
            max: null
        };

        // Simple neural network weights
        this.weights = {
            input: this.initializeMatrix(this.features, 32),
            hidden1: this.initializeMatrix(32, 16),
            hidden2: this.initializeMatrix(16, 8),
            output: this.initializeMatrix(8, 1),
            recurrent: this.initializeMatrix(8, 8) // For memory
        };

        this.biases = {
            hidden1: new Array(32).fill(0).map(() => Math.random() * 0.1 - 0.05),
            hidden2: new Array(16).fill(0).map(() => Math.random() * 0.1 - 0.05),
            output: new Array(8).fill(0).map(() => Math.random() * 0.1 - 0.05)
        };

        this.hiddenState = new Array(8).fill(0); // Memory state
        this.learningRate = 0.001;
        this.onlineLearning = true; // Enable continuous learning
        this.recentPredictions = []; // Store recent predictions for online learning
        this.maxRecentPredictions = 50; // Keep last 50 predictions for learning

        // Model persistence
        this.modelPath = path.join(__dirname, 'saved_model.json');
        this.lastSaved = null;
        this.modelStatus = 'New';
    }

    // Initialize weight matrix with random values
    initializeMatrix(rows, cols) {
        const matrix = [];
        for (let i = 0; i < rows; i++) {
            matrix[i] = [];
            for (let j = 0; j < cols; j++) {
                matrix[i][j] = Math.random() * 0.2 - 0.1; // Random between -0.1 and 0.1
            }
        }
        return matrix;
    }

    // Activation functions
    sigmoid(x) {
        return 1 / (1 + Math.exp(-x));
    }

    tanh(x) {
        return Math.tanh(x);
    }

    relu(x) {
        return Math.max(0, x);
    }

    // Matrix multiplication
    matrixMultiply(a, b) {
        const result = [];
        for (let i = 0; i < a.length; i++) {
            result[i] = [];
            for (let j = 0; j < b[0].length; j++) {
                let sum = 0;
                for (let k = 0; k < b.length; k++) {
                    sum += a[i][k] * b[k][j];
                }
                result[i][j] = sum;
            }
        }
        return result;
    }

    // Vector addition
    vectorAdd(a, b) {
        return a.map((val, idx) => val + b[idx]);
    }

    // Check if current data is outside the scaler range and needs recalibration
    needsScalerRecalibration(data) {
        if (!this.scaler.min || !this.scaler.max) return true;

        // Check if current prices are significantly outside the trained range
        const currentPrices = data.slice(-10).map(d => d.close); // Last 10 prices
        const avgCurrentPrice = currentPrices.reduce((sum, p) => sum + p, 0) / currentPrices.length;

        const scalerRange = this.scaler.max[3] - this.scaler.min[3]; // Close price range
        const scalerMid = this.scaler.min[3] + scalerRange / 2;

        // If current price is more than 20% outside the scaler range, recalibrate
        const deviation = Math.abs(avgCurrentPrice - scalerMid) / scalerRange;

        console.log(`Scaler Check: Current avg price: ${avgCurrentPrice.toFixed(0)}, Scaler mid: ${scalerMid.toFixed(0)}, Deviation: ${(deviation * 100).toFixed(1)}%`);

        return deviation > 0.2; // Recalibrate if more than 20% deviation
    }

    // Normalize data to 0-1 range
    normalizeData(data) {
        // Check if we need to recalibrate the scaler
        if (this.needsScalerRecalibration(data)) {
            console.log("Recalibrating scaler due to price range change...");
            this.scaler.min = null;
            this.scaler.max = null;
        }

        if (!this.scaler.min || !this.scaler.max) {
            // Calculate min/max for each feature
            this.scaler.min = new Array(this.features).fill(Infinity);
            this.scaler.max = new Array(this.features).fill(-Infinity);

            data.forEach(candle => {
                const values = [candle.open, candle.high, candle.low, candle.close, candle.volume];
                values.forEach((val, idx) => {
                    this.scaler.min[idx] = Math.min(this.scaler.min[idx], val);
                    this.scaler.max[idx] = Math.max(this.scaler.max[idx], val);
                });
            });

            console.log(`New scaler ranges:`);
            console.log(`  Close price: ${this.scaler.min[3].toFixed(0)} - ${this.scaler.max[3].toFixed(0)}`);
        }

        return data.map(candle => {
            const values = [candle.open, candle.high, candle.low, candle.close, candle.volume];
            return values.map((val, idx) => {
                const range = this.scaler.max[idx] - this.scaler.min[idx];
                return range === 0 ? 0 : (val - this.scaler.min[idx]) / range;
            });
        });
    }

    // Denormalize prediction back to original scale
    denormalizePrediction(normalizedValue, featureIndex = 3) { // Default to close price
        const range = this.scaler.max[featureIndex] - this.scaler.min[featureIndex];

        // Since we're using tanh (-1 to 1), we need to convert to 0-1 range first
        const scaledValue = (normalizedValue + 1) / 2; // Convert from [-1,1] to [0,1]

        const denormalizedValue = scaledValue * range + this.scaler.min[featureIndex];

        // Add debugging to understand denormalization
        console.log(`Denormalization Debug:`);
        console.log(`  Normalized value (tanh): ${normalizedValue.toFixed(6)}`);
        console.log(`  Scaled value (0-1): ${scaledValue.toFixed(6)}`);
        console.log(`  Range: ${range.toFixed(0)}`);
        console.log(`  Min: ${this.scaler.min[featureIndex].toFixed(0)}`);
        console.log(`  Max: ${this.scaler.max[featureIndex].toFixed(0)}`);
        console.log(`  Final denormalized: ${denormalizedValue.toFixed(0)}`);

        return denormalizedValue;
    }

    // Prepare sequences for training
    prepareSequences(normalizedData) {
        const sequences = [];
        const targets = [];

        for (let i = this.sequenceLength; i < normalizedData.length; i++) {
            // Input sequence - flatten the sequence for our simple network
            const sequence = normalizedData.slice(i - this.sequenceLength, i);
            const flatSequence = sequence.flat(); // Flatten to 1D array
            sequences.push(flatSequence);

            // Target (next close price)
            targets.push(normalizedData[i][3]); // Close price index
        }

        return { sequences, targets };
    }

    // Forward pass through the network
    forward(input) {
        // Input layer to hidden1
        let hidden1 = [];
        for (let i = 0; i < this.weights.input[0].length; i++) {
            let sum = 0;
            for (let j = 0; j < input.length; j++) {
                sum += input[j] * this.weights.input[j % this.features][i];
            }
            sum += this.biases.hidden1[i];
            hidden1.push(this.tanh(sum));
        }

        // Hidden1 to hidden2
        let hidden2 = [];
        for (let i = 0; i < this.weights.hidden1[0].length; i++) {
            let sum = 0;
            for (let j = 0; j < hidden1.length; j++) {
                sum += hidden1[j] * this.weights.hidden1[j][i];
            }
            sum += this.biases.hidden2[i];
            hidden2.push(this.tanh(sum));
        }

        // Hidden2 to output layer with recurrent connection
        let output = [];
        for (let i = 0; i < this.weights.hidden2[0].length; i++) {
            let sum = 0;
            for (let j = 0; j < hidden2.length; j++) {
                sum += hidden2[j] * this.weights.hidden2[j][i];
            }
            // Add recurrent connection
            for (let j = 0; j < this.hiddenState.length; j++) {
                sum += this.hiddenState[j] * this.weights.recurrent[j][i];
            }
            sum += this.biases.output[i];
            output.push(this.tanh(sum));
        }

        // Update hidden state for next prediction
        this.hiddenState = [...output];

        // Final output (single value)
        let finalOutput = 0;
        for (let i = 0; i < output.length; i++) {
            finalOutput += output[i] * this.weights.output[i][0];
        }

        // Use tanh instead of sigmoid to allow negative predictions
        // This helps the model predict both increases and decreases
        return this.tanh(finalOutput); // Return prediction that can be negative
    }

    // Simple training with gradient descent
    async trainModel(historicalData, epochs = 100) {
        if (this.isTraining) {
            console.log("Model is already training...");
            return false;
        }

        if (historicalData.length < this.sequenceLength + 50) {
            console.log("Not enough historical data for training");
            return false;
        }

        this.isTraining = true;

        // Try to load existing model first
        if (this.modelStatus === 'New') {
            console.log("Attempting to load existing model...");
            this.loadModel();
        }

        console.log("Starting/continuing neural network training...");

        try {
            // Normalize data
            const normalizedData = this.normalizeData(historicalData);

            // Prepare sequences
            const { sequences, targets } = this.prepareSequences(normalizedData);

            // Simple training loop
            for (let epoch = 0; epoch < epochs; epoch++) {
                let totalLoss = 0;

                for (let i = 0; i < sequences.length; i++) {
                    const prediction = this.forward(sequences[i]);
                    const error = targets[i] - prediction;
                    totalLoss += error * error;

                    // Simple weight updates (very basic gradient descent)
                    const learningRate = this.learningRate * (1 - epoch / epochs); // Decay learning rate

                    // Update output weights with some regularization
                    for (let j = 0; j < this.weights.output.length; j++) {
                        const weightUpdate = learningRate * error * this.hiddenState[j];
                        // Add small random noise to prevent getting stuck
                        const noise = (Math.random() - 0.5) * 0.001;
                        this.weights.output[j][0] += weightUpdate + noise;

                        // Simple weight decay to prevent overfitting
                        this.weights.output[j][0] *= 0.9999;
                    }
                }

                if (epoch % 20 === 0) {
                    const avgLoss = totalLoss / sequences.length;
                    console.log(`Epoch ${epoch}: Average Loss = ${avgLoss.toFixed(6)}`);
                }
            }

            console.log("Neural network training completed");

            // Auto-save model after training
            this.saveModel();

            this.isTraining = false;
            return true;

        } catch (error) {
            console.error("Error training neural network:", error);
            this.isTraining = false;
            return false;
        }
    }

    // Online learning - update model with new data point
    updateWithNewData(actualPrice, predictedPrice, inputSequence) {
        if (!this.onlineLearning || this.isTraining) {
            return;
        }

        try {
            // Calculate prediction error
            const error = actualPrice - predictedPrice;

            // Store this prediction for learning
            this.recentPredictions.push({
                input: inputSequence,
                predicted: predictedPrice,
                actual: actualPrice,
                error: error
            });

            // Keep only recent predictions
            if (this.recentPredictions.length > this.maxRecentPredictions) {
                this.recentPredictions.shift();
            }

            // Perform online weight update if error is significant
            if (Math.abs(error) > 0.01) { // Only update if error > 1%
                const adaptiveLearningRate = this.learningRate * Math.min(1, Math.abs(error));

                // Simple gradient update for output weights
                for (let j = 0; j < this.weights.output.length; j++) {
                    this.weights.output[j][0] += adaptiveLearningRate * error * this.hiddenState[j];
                }

                console.log(`Online learning update: Error=${(error*100).toFixed(2)}%, LR=${adaptiveLearningRate.toFixed(6)}`);
            }

        } catch (error) {
            console.error("Error in online learning update:", error);
        }
    }

    // Make prediction with online learning
    async predict(recentData) {
        if (this.isTraining) {
            return null;
        }

        if (recentData.length < this.sequenceLength) {
            console.log("Not enough recent data for prediction");
            return null;
        }

        try {
            // Get last sequence
            const lastSequence = recentData.slice(-this.sequenceLength);

            // Normalize
            const normalizedSequence = this.normalizeData(lastSequence);

            // Flatten sequence for input
            const flatSequence = normalizedSequence.flat();

            // Make prediction
            const normalizedPrediction = this.forward(flatSequence);

            // Denormalize prediction
            const denormalizedPrediction = this.denormalizePrediction(normalizedPrediction);

            // Calculate confidence based on recent volatility
            const recentPrices = recentData.slice(-10).map(d => d.close);
            const volatility = this.calculateVolatility(recentPrices);
            this.predictionConfidence = Math.max(0.1, 1 - volatility);

            this.lastPrediction = denormalizedPrediction;

            return {
                predictedPrice: denormalizedPrediction,
                confidence: this.predictionConfidence,
                currentPrice: recentData[recentData.length - 1].close,
                inputSequence: flatSequence // Return for online learning
            };

        } catch (error) {
            console.error("Error making prediction:", error);
            return null;
        }
    }

    // Calculate volatility for confidence estimation
    calculateVolatility(prices) {
        if (prices.length < 2) return 1;
        
        const returns = [];
        for (let i = 1; i < prices.length; i++) {
            returns.push((prices[i] - prices[i-1]) / prices[i-1]);
        }
        
        const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
        const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
        
        return Math.sqrt(variance);
    }

    // Generate trading signal with simple threshold
    generateSignal(prediction, currentPrice, threshold = 0.05) { // Increased threshold to 5%
        if (!prediction) return 'hold';

        const priceChange = (prediction.predictedPrice - currentPrice) / currentPrice;
        const minConfidence = 0.3; // Reduced minimum confidence to 30%

        // Enhanced debugging
        console.log(`\n=== SIGNAL GENERATION DEBUG ===`);
        console.log(`Current Price: ${currentPrice.toLocaleString('id-ID')} IDR`);
        console.log(`Predicted Price: ${prediction.predictedPrice.toLocaleString('id-ID')} IDR`);
        console.log(`Price Change: ${(priceChange * 100).toFixed(2)}%`);
        console.log(`Confidence: ${(prediction.confidence * 100).toFixed(1)}%`);
        console.log(`Threshold: ±${(threshold * 100).toFixed(1)}%`);
        console.log(`Min Confidence Required: ${(minConfidence * 100).toFixed(0)}%`);

        // Check confidence first
        if (prediction.confidence < minConfidence) {
            console.log(`❌ HOLD signal: Low confidence ${(prediction.confidence * 100).toFixed(1)}% < ${(minConfidence * 100).toFixed(0)}%`);
            return 'hold';
        }

        // Check buy signal
        if (priceChange > threshold) {
            console.log(`🟢 BUY signal: Price change ${(priceChange * 100).toFixed(2)}% > +${(threshold * 100).toFixed(1)}%`);
            return 'buy';
        }
        // Check sell signal
        else if (priceChange < -threshold) {
            console.log(`🔴 SELL signal: Price change ${(priceChange * 100).toFixed(2)}% < -${(threshold * 100).toFixed(1)}%`);
            return 'sell';
        }

        console.log(`⏸️ HOLD signal: Price change ${(priceChange * 100).toFixed(2)}% within threshold (±${(threshold * 100).toFixed(1)}%)`);
        console.log(`================================\n`);
        return 'hold';
    }

    // Get learning statistics
    getLearningStats() {
        if (this.recentPredictions.length === 0) {
            return {
                totalPredictions: 0,
                averageError: 0,
                accuracy: 0
            };
        }

        const totalError = this.recentPredictions.reduce((sum, pred) => sum + Math.abs(pred.error), 0);
        const averageError = totalError / this.recentPredictions.length;

        // Calculate accuracy (predictions within 2% of actual)
        const accuratePredictions = this.recentPredictions.filter(pred =>
            Math.abs(pred.error / pred.actual) < 0.02
        ).length;
        const accuracy = accuratePredictions / this.recentPredictions.length;

        return {
            totalPredictions: this.recentPredictions.length,
            averageError: averageError,
            accuracy: accuracy,
            recentErrors: this.recentPredictions.slice(-5).map(p => p.error)
        };
    }

    // Save model to file
    saveModel() {
        try {
            const modelData = {
                weights: this.weights,
                biases: this.biases,
                scaler: this.scaler,
                recentPredictions: this.recentPredictions,
                sequenceLength: this.sequenceLength,
                features: this.features,
                learningRate: this.learningRate,
                savedAt: new Date().toISOString()
            };

            fs.writeFileSync(this.modelPath, JSON.stringify(modelData, null, 2));
            this.lastSaved = new Date();
            this.modelStatus = 'Saved';

            console.log(`Model saved to ${this.modelPath}`);
            return true;
        } catch (error) {
            console.error('Error saving model:', error);
            return false;
        }
    }

    // Load model from file
    loadModel() {
        try {
            if (!fs.existsSync(this.modelPath)) {
                console.log('No saved model found, using new model');
                this.modelStatus = 'New';
                return false;
            }

            const modelData = JSON.parse(fs.readFileSync(this.modelPath, 'utf8'));

            // Restore model state
            this.weights = modelData.weights;
            this.biases = modelData.biases;
            this.scaler = modelData.scaler;
            this.recentPredictions = modelData.recentPredictions || [];
            this.sequenceLength = modelData.sequenceLength || this.sequenceLength;
            this.features = modelData.features || this.features;
            this.learningRate = modelData.learningRate || this.learningRate;

            this.lastSaved = new Date(modelData.savedAt);
            this.modelStatus = 'Loaded';

            console.log(`Model loaded from ${this.modelPath}, saved at ${modelData.savedAt}`);
            return true;
        } catch (error) {
            console.error('Error loading model:', error);
            this.modelStatus = 'Error';
            return false;
        }
    }

    // Reset model and delete saved file
    resetModel() {
        try {
            // Delete saved model file
            if (fs.existsSync(this.modelPath)) {
                fs.unlinkSync(this.modelPath);
                console.log('Deleted old model file');
            }

            // Reset scaler
            this.scaler = { min: null, max: null };

            // Reset predictions
            this.recentPredictions = [];

            // Reinitialize weights
            this.weights = {
                input: this.initializeMatrix(this.features, 32),
                hidden1: this.initializeMatrix(32, 16),
                hidden2: this.initializeMatrix(16, 8),
                output: this.initializeMatrix(8, 1),
                recurrent: this.initializeMatrix(8, 8)
            };

            this.biases = {
                hidden1: new Array(32).fill(0).map(() => Math.random() * 0.1 - 0.05),
                hidden2: new Array(16).fill(0).map(() => Math.random() * 0.1 - 0.05),
                output: new Array(8).fill(0).map(() => Math.random() * 0.1 - 0.05)
            };

            this.hiddenState = new Array(8).fill(0);

            this.modelStatus = 'Reset';
            console.log('Model has been reset');

            return true;
        } catch (error) {
            console.error('Error resetting model:', error);
            return false;
        }
    }

    // Get model summary
    getModelInfo() {
        const learningStats = this.getLearningStats();

        return {
            isTraining: this.isTraining,
            hasModel: true,
            lastPrediction: this.lastPrediction,
            confidence: this.predictionConfidence,
            sequenceLength: this.sequenceLength,
            features: this.features,
            modelType: 'Lightweight Neural Network with Online Learning',
            onlineLearning: this.onlineLearning,
            learningStats: learningStats,
            modelStatus: this.modelStatus,
            lastSaved: this.lastSaved
        };
    }
}

module.exports = LSTMPredictor;
