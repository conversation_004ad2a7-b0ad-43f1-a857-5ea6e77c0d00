<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LSTM Trading Bot Dashboard</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'crypto-green': '#00d4aa',
                        'crypto-red': '#ff6b6b',
                        'crypto-blue': '#4dabf7',
                        'dark-bg': '#1a1a1a',
                        'dark-card': '#2d2d2d'
                    }
                }
            }
        }
    </script>

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-running {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .metric-card {
            transition: transform 0.2s ease-in-out;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="font-sans">
    <div class="min-h-screen p-4">
        <!-- Header -->
        <div class="max-w-7xl mx-auto mb-6">
            <div class="glass-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-2">🤖 LSTM Trading Bot</h1>
                        <p class="text-gray-200">AI-Powered Cryptocurrency Trading with Online Learning</p>
                    </div>
                    <div class="flex space-x-4">
                        <button id="startBtn" class="bg-crypto-green hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            Start Bot
                        </button>
                        <button id="stopBtn" class="bg-crypto-red hover:bg-red-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors" disabled>
                            Stop Bot
                        </button>
                        <button id="retrainBtn" class="bg-crypto-blue hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            Retrain Model
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Cards -->
        <div class="max-w-7xl mx-auto mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <!-- Bot Status -->
                <div class="glass-card rounded-xl p-6 metric-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-300 text-sm">Bot Status</p>
                            <p id="botStatus" class="text-xl font-bold text-white">Stopped</p>
                        </div>
                        <div id="statusIndicator" class="w-4 h-4 bg-gray-500 rounded-full"></div>
                    </div>
                </div>

                <!-- Current Price -->
                <div class="glass-card rounded-xl p-6 metric-card">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-gray-300 text-sm">Current Price</p>
                            <p id="currentPrice" class="text-xl font-bold text-white">Rp 0</p>
                            <p id="priceChange24h" class="text-sm font-semibold">+0.00%</p>
                        </div>
                        <div class="text-2xl">💰</div>
                    </div>
                </div>

                <!-- Predicted Price -->
                <div class="glass-card rounded-xl p-6 metric-card">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-gray-300 text-sm">Predicted Price</p>
                            <p id="predictedPrice" class="text-xl font-bold text-white">Rp 0</p>
                            <p id="predictedChange" class="text-sm font-semibold">+0.00%</p>
                        </div>
                        <div class="text-2xl">🔮</div>
                    </div>
                </div>

                <!-- Confidence -->
                <div class="glass-card rounded-xl p-6 metric-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-300 text-sm">Confidence</p>
                            <p id="confidence" class="text-xl font-bold text-white">0%</p>
                        </div>
                        <div class="text-2xl">📊</div>
                    </div>
                </div>

                <!-- Price Movement -->
                <div class="glass-card rounded-xl p-6 metric-card">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-gray-300 text-sm">Price Movement</p>
                            <p id="priceMovement" class="text-xl font-bold text-white">0.00%</p>
                            <p id="priceDirection" class="text-xs text-gray-400">No change</p>
                        </div>
                        <div id="movementIcon" class="text-2xl">📈</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Price Chart -->
            <div class="lg:col-span-2">
                <div class="glass-card rounded-xl p-6">
                    <h2 class="text-xl font-bold text-white mb-4">📈 Price Chart & Predictions</h2>
                    <div class="relative" style="height: 400px;">
                        <canvas id="priceChart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Side Panel -->
            <div class="space-y-6">
                <!-- Trading Signals -->
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-lg font-bold text-white mb-4">🎯 Trading Signal</h3>
                    <div class="text-center">
                        <div id="tradingSignal" class="text-3xl font-bold mb-2">HOLD</div>
                        <p class="text-gray-300 text-sm">Current recommendation</p>
                    </div>
                </div>

                <!-- Portfolio -->
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-lg font-bold text-white mb-4">💼 Portfolio</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-300">IDR Balance:</span>
                            <span id="idrBalance" class="text-white font-semibold">Rp 0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">BTC Balance:</span>
                            <span id="btcBalance" class="text-white font-semibold">0 BTC</span>
                        </div>
                        <hr class="border-gray-600">
                        <div class="flex justify-between">
                            <span class="text-gray-300 font-semibold">Total Asset:</span>
                            <span id="totalAsset" class="text-crypto-green font-bold">Rp 0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Mode:</span>
                            <span id="tradingMode" class="text-crypto-green font-semibold">Test</span>
                        </div>
                    </div>
                </div>

                <!-- Model Info -->
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-lg font-bold text-white mb-4">🧠 AI Model</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Type:</span>
                            <span class="text-white text-xs">LSTM</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Learning:</span>
                            <span id="onlineLearning" class="text-crypto-green font-semibold">Online</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Accuracy:</span>
                            <span id="modelAccuracy" class="text-white font-semibold">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Predictions:</span>
                            <span id="totalPredictions" class="text-white font-semibold">0</span>
                        </div>
                    </div>
                </div>

                <!-- Model Persistence -->
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-lg font-bold text-white mb-4">💾 Model Management</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Model Status:</span>
                            <span id="modelStatus" class="text-crypto-green font-semibold">Loaded</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Saved:</span>
                            <span id="lastSaved" class="text-white text-sm">Never</span>
                        </div>
                        <div class="space-y-2">
                            <button id="saveModelBtn" class="w-full bg-crypto-green hover:bg-green-600 text-white py-2 px-4 rounded-lg font-semibold transition-colors">
                                Save Model
                            </button>
                            <button id="loadModelBtn" class="w-full bg-crypto-blue hover:bg-blue-600 text-white py-2 px-4 rounded-lg font-semibold transition-colors">
                                Load Model
                            </button>
                            <button id="resetModelBtn" class="w-full bg-crypto-red hover:bg-red-600 text-white py-2 px-4 rounded-lg font-semibold transition-colors">
                                Reset Model
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scalping & Technical Analysis -->
        <div class="max-w-7xl mx-auto mt-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Scalping Panel -->
                <div class="glass-card rounded-xl p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-white">⚡ Scalping</h2>
                        <button id="toggleScalpingBtn" class="bg-crypto-blue hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                            Toggle Scalping
                        </button>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Status:</span>
                            <span id="scalpingStatus" class="text-crypto-green font-semibold">Enabled</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Active Trades:</span>
                            <span id="activeScalpTrades" class="text-white font-semibold">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Win Rate:</span>
                            <span id="scalpWinRate" class="text-white font-semibold">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Total Profit:</span>
                            <span id="scalpTotalProfit" class="text-crypto-green font-semibold">Rp 0</span>
                        </div>
                    </div>
                </div>

                <!-- Technical Analysis Panel -->
                <div class="glass-card rounded-xl p-6">
                    <h2 class="text-xl font-bold text-white mb-4">📊 Technical Analysis</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-300">RSI:</span>
                            <span id="rsiValue" class="text-white font-semibold">50.0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">MACD:</span>
                            <span id="macdValue" class="text-white font-semibold">0.0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Support Levels:</span>
                            <span id="supportCount" class="text-white font-semibold">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Resistance Levels:</span>
                            <span id="resistanceCount" class="text-white font-semibold">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Enhanced Signal:</span>
                            <span id="enhancedSignal" class="text-gray-300 font-semibold">HOLD</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trade Log -->
        <div class="max-w-7xl mx-auto mt-6">
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-xl font-bold text-white mb-4">📋 Trade Log</h2>
                <div id="tradeLog" class="space-y-2 max-h-60 overflow-y-auto">
                    <p class="text-gray-400 text-center py-4">No trades yet...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>