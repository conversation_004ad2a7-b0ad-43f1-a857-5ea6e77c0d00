<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indodax Trading Bot</title>
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .status-dot {
            height: 12px;
            width: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-running { background-color: #22c55e; } /* green-500 */
        .status-stopped { background-color: #ef4444; } /* red-500 */
        .status-loading { background-color: #f59e0b; } /* amber-500 */
        
        /* Custom checkbox style */
        .toggle-checkbox:checked {
            right: 0;
            border-color: #48bb78;
        }
        .toggle-checkbox:checked + .toggle-label {
            background-color: #48bb78;
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-200">

    <div class="container mx-auto p-4 md:p-8">

        <!-- Header -->
        <header class="flex justify-between items-center mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-white">Indodax Trading Bot</h1>
            <div id="bot-status-indicator" class="flex items-center space-x-3 bg-gray-800 px-4 py-2 rounded-lg">
                <span id="status-dot" class="status-dot status-stopped"></span>
                <span id="status-text" class="font-medium">Stopped</span>
            </div>
        </header>

        <!-- Controls -->
        <div class="mb-8 p-6 bg-gray-800 rounded-xl shadow-lg">
            <h2 class="text-xl font-semibold mb-4 text-white">Controls</h2>
            <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-6">
                <div class="flex space-x-4 mb-4 sm:mb-0">
                    <button id="start-btn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors disabled:opacity-50">Start Bot</button>
                    <button id="stop-btn" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition-colors disabled:opacity-50">Stop Bot</button>
                </div>
                 <!-- Test Mode Toggle -->
                <div class="flex items-center">
                    <input type="checkbox" id="test-mode-checkbox" class="hidden" checked>
                    <label for="test-mode-checkbox" class="flex items-center cursor-pointer">
                        <div class="relative">
                            <div class="w-14 h-8 bg-gray-600 rounded-full shadow-inner"></div>
                            <div id="toggle-switch" class="absolute w-6 h-6 bg-white rounded-full shadow inset-y-0 left-0 my-1 ml-1 transition-transform duration-300 ease-in-out transform translate-x-0"></div>
                        </div>
                        <div class="ml-3 text-gray-300 font-medium">
                            <span id="test-mode-label">Test Mode</span>
                        </div>
                    </label>
                </div>
            </div>
             <p class="text-sm text-gray-500 mt-4">
                <strong>Test Mode:</strong> Simulates trades with a virtual balance. No real money is used. <br>
                <strong>Live Mode:</strong> Executes real trades on your Indodax account. Use with caution.
            </p>
        </div>

        <!-- Dashboard -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Market Info -->
            <div class="bg-gray-800 p-6 rounded-xl shadow-lg">
                <h3 class="font-semibold text-lg mb-2 text-gray-400">BTC/IDR Market</h3>
                <p id="last-price" class="text-3xl font-bold text-white">Loading...</p>
            </div>
            <!-- SMA Info -->
            <div class="bg-gray-800 p-6 rounded-xl shadow-lg">
                 <h3 class="font-semibold text-lg mb-2 text-gray-400">SMA Crossover</h3>
                 <p class="text-sm text-green-400">Short (20): <span id="sma-short" class="font-mono">...</span></p>
                 <p class="text-sm text-blue-400">Long (50): <span id="sma-long" class="font-mono">...</span></p>
            </div>
             <!-- Account Balance -->
            <div class="bg-gray-800 p-6 rounded-xl shadow-lg relative">
                <span id="balance-mode-badge" class="absolute top-2 right-2 text-xs font-bold bg-yellow-500 text-gray-900 px-2 py-1 rounded-full">TEST</span>
                <h3 class="font-semibold text-lg mb-2 text-gray-400">Balance</h3>
                <p id="balance-idr" class="text-lg">IDR: Loading...</p>
                <p id="balance-btc" class="text-lg">BTC: Loading...</p>
            </div>
        </div>
        
        <!-- Chart -->
        <div class="mb-8 bg-gray-800 p-4 md:p-6 rounded-xl shadow-lg">
            <h2 class="text-xl font-semibold mb-4 text-white">Price Chart</h2>
            <canvas id="priceChart"></canvas>
        </div>


        <!-- Trade Log -->
        <div class="bg-gray-800 p-6 rounded-xl shadow-lg">
            <h2 class="text-xl font-semibold mb-4 text-white">Trade Log</h2>
            <div id="trade-log" class="h-64 overflow-y-auto bg-gray-900 p-4 rounded-md font-mono text-sm">
                <p class="text-gray-500">Waiting for bot activity...</p>
            </div>
        </div>

    </div>

    <script>
        // DOM Elements
        const statusDot = document.getElementById('status-dot');
        const statusText = document.getElementById('status-text');
        const startBtn = document.getElementById('start-btn');
        const stopBtn = document.getElementById('stop-btn');
        const testModeCheckbox = document.getElementById('test-mode-checkbox');
        const toggleSwitch = document.getElementById('toggle-switch');
        const testModeLabel = document.getElementById('test-mode-label');
        const lastPriceEl = document.getElementById('last-price');
        const smaShortEl = document.getElementById('sma-short');
        const smaLongEl = document.getElementById('sma-long');
        const balanceIdrEl = document.getElementById('balance-idr');
        const balanceBtcEl = document.getElementById('balance-btc');
        const tradeLogEl = document.getElementById('trade-log');
        const balanceModeBadge = document.getElementById('balance-mode-badge');
        
        let priceChart;

        // --- Chart Initialization ---
        function initializeChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            priceChart = new Chart(ctx, {
                type: 'line',
                data: { datasets: [{ label: 'BTC/IDR Price', borderColor: 'rgb(255, 255, 255)', data: [], tension: 0.1, borderWidth: 2 }, { label: 'SMA Short (20)', borderColor: 'rgb(74, 222, 128)', data: [], borderWidth: 1.5, pointRadius: 0 }, { label: 'SMA Long (50)', borderColor: 'rgb(96, 165, 250)', data: [], borderWidth: 1.5, pointRadius: 0 }] },
                options: { responsive: true, scales: { x: { type: 'time', time: { unit: 'minute' }, grid: { color: 'rgba(255, 255, 255, 0.1)' } }, y: { grid: { color: 'rgba(255, 255, 255, 0.1)' } } } }
            });
        }
        
        // --- Data Fetching and UI Update ---
        async function updateUI() {
            try {
                const response = await fetch('/api/data');
                if (!response.ok) return;
                const data = await response.json();

                // Update Status Indicator
                statusText.textContent = data.status + (data.is_running ? (data.is_test_mode ? ' (Test)' : ' (Live)') : '');
                statusDot.className = 'status-dot';
                if (data.is_running) {
                    statusDot.classList.add('status-running');
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                    testModeCheckbox.disabled = true;
                } else {
                    statusDot.classList.add(data.status.toLowerCase().includes('error') ? 'status-stopped' : 'status-stopped');
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    testModeCheckbox.disabled = false;
                }

                // Update Dashboard
                lastPriceEl.textContent = `Rp ${Number(data.last_price).toLocaleString('id-ID')}`;
                smaShortEl.textContent = Number(data.sma_short).toLocaleString('id-ID');
                smaLongEl.textContent = Number(data.sma_long).toLocaleString('id-ID');
                
                // Update Balance and Badge
                balanceModeBadge.textContent = data.is_test_mode ? "TEST" : "LIVE";
                balanceModeBadge.className = data.is_test_mode 
                    ? 'absolute top-2 right-2 text-xs font-bold bg-yellow-500 text-gray-900 px-2 py-1 rounded-full'
                    : 'absolute top-2 right-2 text-xs font-bold bg-red-600 text-white px-2 py-1 rounded-full';
                
                const idrBalance = data.is_test_mode ? data.test_balance_idr : data.balance_idr;
                const btcBalance = data.is_test_mode ? data.test_balance_btc : data.balance_btc;
                balanceIdrEl.textContent = `IDR: ${Number(idrBalance).toLocaleString('id-ID')}`;
                balanceBtcEl.textContent = `BTC: ${Number(btcBalance).toFixed(8)}`;

                // Update Trade Log
                tradeLogEl.innerHTML = data.trade_log && data.trade_log.length > 0 ? data.trade_log.map(log => `<p>${log}</p>`).join('') : '<p class="text-gray-500">No trades yet.</p>';
                
                // Update Chart
                if (data.historical_data && data.historical_data.length > 0 && priceChart) {
                    const priceData = data.historical_data.map(d => ({x: new Date(d.timestamp), y: d.close}));
                    const smaShortData = data.historical_data.map(d => ({x: new Date(d.timestamp), y: d.sma_short})).filter(d => d.y);
                    const smaLongData = data.historical_data.map(d => ({x: new Date(d.timestamp), y: d.sma_long})).filter(d => d.y);
                    priceChart.data.datasets[0].data = priceData;
                    priceChart.data.datasets[1].data = smaShortData;
                    priceChart.data.datasets[2].data = smaLongData;
                    priceChart.update('none');
                }

            } catch (error) {
                console.error('Error updating UI:', error);
                statusText.textContent = "Connection Error";
                statusDot.className = 'status-dot status-stopped';
            }
        }

        // --- Event Listeners ---
        startBtn.addEventListener('click', async () => {
            const isTestMode = testModeCheckbox.checked;
            statusText.textContent = 'Starting...';
            statusDot.className = 'status-dot status-loading';
            await fetch('/api/start', { 
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ isTestMode })
            });
        });

        stopBtn.addEventListener('click', async () => {
            statusText.textContent = 'Stopping...';
            statusDot.className = 'status-dot status-loading';
            await fetch('/api/stop', { method: 'POST' });
        });
        
        testModeCheckbox.addEventListener('change', () => {
            const isChecked = testModeCheckbox.checked;
            toggleSwitch.style.transform = isChecked ? 'translateX(1.75rem)' : 'translateX(0)';
            testModeLabel.textContent = isChecked ? 'Test Mode' : 'Live Mode';
        });

        // --- Initialization ---
        document.addEventListener('DOMContentLoaded', () => {
            initializeChart();
            updateUI(); // Initial fetch
            setInterval(updateUI, 5000); // Poll every 5 seconds
            
            // Set initial toggle state
            const isChecked = testModeCheckbox.checked;
            toggleSwitch.style.transform = isChecked ? 'translateX(1.75rem)' : 'translateX(0)';
            testModeLabel.textContent = isChecked ? 'Test Mode' : 'Live Mode';
            stopBtn.disabled = true;
        });
    </script>
</body>
</html>
